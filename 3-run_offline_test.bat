@echo off
set ADB_PATH=.\platform-tools

echo GPU_QCOM_setFreq_mid-3
adb shell cat /sys/class/kgsl/kgsl-3d0/gpuclk
adb shell cat /sys/class/kgsl/kgsl-3d0/devfreq/governor

adb shell "echo 1 > /sys/class/kgsl/kgsl-3d0/force_rail_on"
adb shell "echo 1 > /sys/class/kgsl/kgsl-3d0/force_clk_on"
adb shell "echo 1 > /sys/class/kgsl/kgsl-3d0/force_bus_on"
adb shell "echo 10000000 > /sys/class/kgsl/kgsl-3d0/idle_timer"
adb shell "echo 3 > /sys/class/kgsl/kgsl-3d0/min_pwrlevel"
adb shell "echo 3 > /sys/class/kgsl/kgsl-3d0/max_pwrlevel"
adb shell "echo performance > /sys/class/kgsl/kgsl-3d0/devfreq/governor"

adb shell cat /sys/class/kgsl/kgsl-3d0/gpuclk
adb shell cat /sys/class/kgsl/kgsl-3d0/devfreq/governor

echo CPU_QCOM_setAllFreq_capture
adb root

adb shell stop mpdecision
adb shell setprop setenforce 0
adb shell setprop persist.sys.hypnus.daemon.enable 0
adb shell setprop oppo.camera.hypnus.enable 0

:: only my evt wukong (NF+1)/2+2, right (NF+1)/2
adb shell "echo userspace > /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor"
adb shell "awk '{print $(NF-8)}' /sys/devices/system/cpu/cpu0/cpufreq/scaling_available_frequencies > /data/local/tmp/cpuFreqTmp.txt"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu0/cpufreq/scaling_min_freq"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu0/cpufreq/scaling_max_freq"

adb shell "echo userspace > /sys/devices/system/cpu/cpu1/cpufreq/scaling_governor"
adb shell "awk '{print $(NF-8)}' /sys/devices/system/cpu/cpu1/cpufreq/scaling_available_frequencies > /data/local/tmp/cpuFreqTmp.txt"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu1/cpufreq/scaling_min_freq"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu1/cpufreq/scaling_max_freq"

adb shell "echo userspace > /sys/devices/system/cpu/cpu2/cpufreq/scaling_governor"
adb shell "awk '{print $(NF-8)}' /sys/devices/system/cpu/cpu2/cpufreq/scaling_available_frequencies > /data/local/tmp/cpuFreqTmp.txt"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu2/cpufreq/scaling_min_freq"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu2/cpufreq/scaling_max_freq"

adb shell "echo userspace > /sys/devices/system/cpu/cpu3/cpufreq/scaling_governor"
adb shell "awk '{print $(NF)}' /sys/devices/system/cpu/cpu3/cpufreq/scaling_available_frequencies > /data/local/tmp/cpuFreqTmp.txt"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu3/cpufreq/scaling_min_freq"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu3/cpufreq/scaling_max_freq"

adb shell "echo userspace > /sys/devices/system/cpu/cpu4/cpufreq/scaling_governor"
adb shell "awk '{print $(NF)}' /sys/devices/system/cpu/cpu4/cpufreq/scaling_available_frequencies > /data/local/tmp/cpuFreqTmp.txt"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu4/cpufreq/scaling_min_freq"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu4/cpufreq/scaling_max_freq"

adb shell "echo userspace > /sys/devices/system/cpu/cpu5/cpufreq/scaling_governor"
adb shell "awk '{print $(NF)}' /sys/devices/system/cpu/cpu5/cpufreq/scaling_available_frequencies > /data/local/tmp/cpuFreqTmp.txt"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu5/cpufreq/scaling_min_freq"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu5/cpufreq/scaling_max_freq"

adb shell "echo userspace > /sys/devices/system/cpu/cpu6/cpufreq/scaling_governor"
adb shell "awk '{print $(NF)}' /sys/devices/system/cpu/cpu6/cpufreq/scaling_available_frequencies > /data/local/tmp/cpuFreqTmp.txt"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu6/cpufreq/scaling_min_freq"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu6/cpufreq/scaling_max_freq"

adb shell "echo userspace > /sys/devices/system/cpu/cpu7/cpufreq/scaling_governor"
adb shell "awk '{print $(NF-1)}' /sys/devices/system/cpu/cpu7/cpufreq/scaling_available_frequencies > /data/local/tmp/cpuFreqTmp.txt"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu7/cpufreq/scaling_min_freq"
adb shell "echo $(cat /data/local/tmp/cpuFreqTmp.txt) > /sys/devices/system/cpu/cpu7/cpufreq/scaling_max_freq"

adb shell "rm /data/local/tmp/cpuFreqTmp.txt"

adb shell cat /sys/devices/system/cpu/cpu*/cpufreq/scaling_cur_freq

adb shell setenforce 0
@REM adb shell setprop persist.camera.hair.refine.log.level     31
adb shell setprop persist.camera.bokeh.log.level 31
adb shell setprop persist.camera.hair.refine.bypass.all 0
adb shell setprop persist.camera.bokeh.enable.memory.trace  1
adb shell setprop persist.camera.hair.refine.memory.trace   0
adb shell setprop persist.camera.bokeh.rectify.dump 0

rem hair matting ON
adb shell setprop persist.camera.bokeh.hairmatting.enable 1

rem hair matting OFF
rem adb shell setprop persist.camera.bokeh.hairmatting.enable 0

rem addnoise OFF
adb shell setprop persist.camera.bokeh.bypass.render.addnoise.enable 1
adb shell setprop persist.camera.bokeh.enable.func.calltrace 1
adb shell setprop persist.camera.bokeh.bypass.stereoai.wait.hr 1

for /f "usebackq delims=" %%A in (`adb shell getprop persist.camera.bokeh.hairmatting.enable`) do set HAIR_MATTING=%%A
echo Hair matting enable switch is %HAIR_MATTING%

echo.
echo ============= Bokeh Simulation Start ==============
echo.

echo [1] bokeh simulation directory
adb shell mkdir -p /data/local/tmp/BokehSim
adb shell mkdir -p /data/local/tmp/BokehSim/SUCAI
adb shell rm -rf /data/local/tmp/BokehSim/RESULT/
adb shell mkdir -p /data/local/tmp/BokehSim/RESULT
echo.

@REM adb shell mkdir -p /sdcard/Android/data/com.oplus.camera/files/spdebug/bokehdump/
@REM adb shell rm -rf /sdcard/Android/data/com.oplus.camera/files/spdebug/bokehdump/*
echo [2] push test files
@REM adb push --sync dualcamdepth_capture\. /data/local/tmp/BokehSim/SUCAI
echo.

echo [3] push test program: test_arcbokehsim
adb push libs\arm64-v8a\test_arcbokehsim /data/local/tmp/BokehSim
adb push libs\arm64-v8a\libOPAlgoCamCaptureDualPortrait.so /odm/lib64/
adb push ..\..\test\Test_RearPortraitBokeh\libImageLoadSave\libImageLoadSave.so /odm/lib64/
echo.

echo [4] execute program
adb shell chmod 777 /data/local/tmp/BokehSim/test_arcbokehsim
adb shell mkdir -p  /data/local/tmp/BokehSim/bokeh
adb shell "cd /data/local/tmp/BokehSim && ./test_arcbokehsim SUCAI 1"
echo.

for /f "tokens=1,2 delims=:" %%i in ('time/t') do set t=%%i%%j
set resultDir=RESULT_%date:~0,4%%date:~5,2%%date:~8,2%_%t:~0,4%%time:~6,2%

adb pull            /data/local/tmp/BokehSim/RESULT           .\%resultDir%
echo result stored in %resultDir%
@REM adb pull            /sdcard/Android/data/com.oplus.camera/files/spdebug/bokehdump

rem addnoise ON
adb shell setprop persist.camera.bokeh.enable.func.calltrace 0
adb shell setprop persist.camera.bokeh.bypass.render.addnoise.enable 0
adb shell setprop persist.camera.bokeh.bypass.stereoai.wait.hr 0
echo ============= Bokeh Simulation End ==============

pause
