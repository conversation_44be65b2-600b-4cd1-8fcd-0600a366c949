#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comparison module for Bokeh Verification Tool.

This module handles comparing results between different runs.
"""

import os
import logging
from typing import Dict, List, Tuple, Any, Optional

try:
    from PIL import Image, ImageChops, ImageStat
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("Warning: PIL not available. Image comparison will be limited.")

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("Warning: NumPy not available. Some comparison features will be limited.")

from config import Config
from utils import find_files, ensure_directory

class ResultComparator:
    """Comparator for Bokeh results."""

    def __init__(self, config: Config):
        """
        Initialize the result comparator.

        Args:
            config: Configuration object.
        """
        self.logger = logging.getLogger('bokeh_verify.compare')
        self.config = config

        # Comparison thresholds
        self.global_threshold = config.getfloat('compare', 'global_threshold', 5.0)
        self.module_thresholds = config.get_module_thresholds()

        # Output settings
        self.save_all_images = config.getboolean('output', 'save_all_images', True)
        self.save_diff_only = config.getboolean('output', 'save_diff_only', False)

    def compare(self, result_path: str, reference_path: str) -> Dict[str, Any]:
        """
        Compare results with reference data.

        Args:
            result_path: Path to the result data.
            reference_path: Path to the reference data.

        Returns:
            Dictionary with comparison results.
        """
        self.logger.info(f"Comparing results in {result_path} with reference in {reference_path}")

        # Find all image files in result and reference paths
        result_files = find_files(result_path, '.ppm')
        reference_files = find_files(reference_path, '.ppm')

        if not result_files:
            self.logger.error(f"No result files found in {result_path}")
            return {'pass': False, 'error': f"No result files found in {result_path}"}

        if not reference_files:
            self.logger.error(f"No reference files found in {reference_path}")
            return {'pass': False, 'error': f"No reference files found in {reference_path}"}

        # Map files by name for easier comparison
        result_files_map = {os.path.basename(f): f for f in result_files}
        reference_files_map = {os.path.basename(f): f for f in reference_files}

        # Compare files
        comparison_results = []
        max_diff = 0.0
        failed_modules = []

        for filename, result_file in result_files_map.items():
            if filename in reference_files_map:
                reference_file = reference_files_map[filename]

                # Extract module name from filename
                module = self._extract_module_from_filename(filename)
                threshold = self._get_threshold_for_module(module)

                # Compare images
                diff, diff_image = self._compare_images(result_file, reference_file)

                # Check if diff exceeds threshold
                passed = diff <= threshold
                if not passed:
                    failed_modules.append(module)

                # Update max diff
                max_diff = max(max_diff, diff)

                # Add to results
                comparison_results.append({
                    'filename': filename,
                    'module': module,
                    'result_file': result_file,
                    'reference_file': reference_file,
                    'diff': diff,
                    'threshold': threshold,
                    'passed': passed,
                    'diff_image': diff_image
                })
            else:
                self.logger.warning(f"Reference file not found for {filename}")
                comparison_results.append({
                    'filename': filename,
                    'module': self._extract_module_from_filename(filename),
                    'result_file': result_file,
                    'reference_file': None,
                    'diff': float('inf'),
                    'threshold': self.global_threshold,
                    'passed': False,
                    'diff_image': None
                })
                failed_modules.append(self._extract_module_from_filename(filename))

        # Check for missing result files
        missing_files = []
        for filename, reference_file in reference_files_map.items():
            if filename not in result_files_map:
                self.logger.warning(f"Result file not found for {filename}")
                missing_files.append(filename)
                failed_modules.append(self._extract_module_from_filename(filename))

        # Determine overall pass/fail
        passed = len(failed_modules) == 0

        # Prepare result summary
        result_summary = {
            'pass': passed,
            'max_diff': max_diff,
            'failed_modules': list(set(failed_modules)),
            'missing_files': missing_files,
            'comparison_results': comparison_results,
            'result_path': result_path,
            'reference_path': reference_path
        }

        if passed:
            self.logger.info("Comparison PASSED! All modules within threshold.")
        else:
            self.logger.warning(f"Comparison FAILED! Failed modules: {list(set(failed_modules))}")
            self.logger.warning(f"Max diff: {max_diff}")

        return result_summary

    def _compare_images(self, image1_path: str, image2_path: str) -> Tuple[float, Optional[Any]]:
        """
        Compare two images and calculate the difference.

        Args:
            image1_path: Path to the first image.
            image2_path: Path to the second image.

        Returns:
            Tuple of (difference value, difference image).
        """
        if not PIL_AVAILABLE:
            # Simple file size comparison if PIL is not available
            try:
                size1 = os.path.getsize(image1_path)
                size2 = os.path.getsize(image2_path)
                diff = abs(size1 - size2) / max(size1, size2) * 100 if max(size1, size2) > 0 else 0
                return diff, None
            except Exception as e:
                self.logger.error(f"Error comparing file sizes: {str(e)}")
                return float('inf'), None

        try:
            img1 = Image.open(image1_path)
            img2 = Image.open(image2_path)

            # Check if images have the same size
            if img1.size != img2.size:
                self.logger.warning(f"Image sizes don't match: {img1.size} vs {img2.size}")
                return float('inf'), None

            # Calculate difference
            diff_img = ImageChops.difference(img1, img2)

            # Calculate statistics
            stat = ImageStat.Stat(diff_img)
            diff_avg = float(sum(stat.mean)) / len(stat.mean)

            return diff_avg, diff_img

        except Exception as e:
            self.logger.error(f"Error comparing images: {str(e)}")
            return float('inf'), None

    def _extract_module_from_filename(self, filename: str) -> str:
        """
        Extract module name from filename.

        Args:
            filename: Filename to extract module from.

        Returns:
            Module name.
        """
        # Map of keywords to module names
        module_keywords = {
            'depth': 'depth',
            'render': 'render',
            'rectify': 'rectify',
            'final_result': 'final',
            'bokehOutImg': 'render',
            'alphamap': 'render',
            'blurmap': 'render',
            'edgemap': 'render',
            'gather': 'render',
            'input_yuv': 'input',
            'input_render': 'input',
            'inputY': 'input',
            'inputUV': 'input',
            'turbo': 'depth',
            'refine': 'depth',
            'rough': 'depth',
        }

        # Check for keywords in filename
        for keyword, module in module_keywords.items():
            if keyword in filename:
                return module

        # Default to 'unknown' if no match
        return 'unknown'

    def _get_threshold_for_module(self, module: str) -> float:
        """
        Get the threshold for a specific module.

        Args:
            module: Module name.

        Returns:
            Threshold value.
        """
        # Check if we have a specific threshold for this module
        if module in self.module_thresholds:
            return self.module_thresholds[module]

        # Fall back to global threshold
        return self.global_threshold

    def save_comparison_images(self, comparison_results: List[Dict[str, Any]], output_dir: str) -> Dict[str, str]:
        """
        Save comparison images to output directory.

        Args:
            comparison_results: List of comparison results.
            output_dir: Directory to save images to.

        Returns:
            Dictionary mapping filenames to saved image paths.
        """
        if not PIL_AVAILABLE:
            self.logger.warning("PIL not available, skipping image saving")
            return {}

        self.logger.info(f"Saving comparison images to {output_dir}")

        # Create output directory
        ensure_directory(output_dir)

        saved_images = {}

        for result in comparison_results:
            filename = result['filename']
            passed = result['passed']
            diff_image = result['diff_image']

            # Skip if we only want to save diff images and this one passed
            if self.save_diff_only and passed:
                continue

            # Skip if we don't have a diff image
            if diff_image is None:
                continue

            # Save diff image
            diff_path = os.path.join(output_dir, f"diff_{filename}.png")
            try:
                diff_image.save(diff_path)
                saved_images[filename] = diff_path
            except Exception as e:
                self.logger.error(f"Error saving diff image for {filename}: {str(e)}")

        return saved_images
