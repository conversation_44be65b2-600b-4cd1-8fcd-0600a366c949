#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Push module for Bokeh Verification Tool / Bokeh验证工具推送模块

This module handles pushing libraries and test files to the device.
该模块处理将库和测试文件推送到设备。
"""

import os
import logging
import time
from typing import Optional, List, Dict, Any

try:
    import paramiko
    PARAMIKO_AVAILABLE = True
except ImportError:
    PARAMIKO_AVAILABLE = False
    print("Warning: paramiko not available. SSH functionality will be disabled.")

from config import Config
from utils import run_adb_command, ensure_directory, Timer
import common

class DevicePusher:
    """
    Device pusher for Bokeh SDK / Bokeh SDK设备推送器
    """

    def __init__(self, config: Config):
        """
        Initialize the device pusher.
        初始化设备推送器。

        Args / 参数:
            config: Configuration object / 配置对象
        """
        self.logger = logging.getLogger('bokeh_verify.push')
        self.config = config

        # Device configuration / 设备配置
        self.device_id = config.get('device', 'id', '')
        self.series = config.get('device', 'series', 'X8U')

        # Get SOC configuration / 获取SOC配置
        self.soc_config = common.get_soc_config(self.series)

        # Library paths / 库路径
        self.lib_path = config.get('push', 'lib_path', 'libs/arm64-v8a')
        self.lib_name = config.get('push', 'lib_name', common.LIB_NAME)
        self.target_lib_path = config.get('push', 'target_lib_path', '/odm/lib64')

        # Test program / 测试程序
        self.test_program = config.get('push', 'test_program', 'test_arcbokehsim')
        self.test_program_path = config.get('push', 'test_program_path', '/data/local/tmp/BokehSim')

        # Model paths / 模型路径
        self.model_path = config.get('push', 'model_path', '')
        self.target_model_path = config.get('push', 'target_model_path', '/odm/etc/camera/dualcam_capture_bokeh')

        # Rectify params / 校正参数
        self.rectify_params_path = config.get('push', 'rectify_params_path', '')

        # Auto-detect device if not specified / 如果未指定设备则自动检测
        if not self.device_id:
            devices = common.get_device_id()
            if devices:
                self.device_id = devices[0]
                self.logger.info(f"Auto-detected device / 自动检测到设备: {self.device_id}")
            else:
                self.logger.warning("No devices found / 未找到设备")

    def push_libraries(self, use_original_script: bool = False) -> bool:
        """
        Push libraries to the device.
        将库推送到设备。

        Args / 参数:
            use_original_script: Whether to use original 2-push script / 是否使用原始的2-push脚本

        Returns / 返回:
            True if successful, False otherwise / 如果成功则返回True，否则返回False
        """
        if use_original_script:
            return self._push_using_original_script()
        else:
            return self._push_using_internal_logic()

    def _push_using_original_script(self) -> bool:
        """
        Push libraries using the original 2-push_dualcambokeh_sdk.py script.
        使用原始的2-push_dualcambokeh_sdk.py脚本推送库。

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        # Check if SSH is configured (script should be downloaded from remote)
        # 检查是否配置了SSH（脚本应该从远程下载）
        ssh_host = self.config.get('device', 'ssh_host', '')
        ssh_user = self.config.get('device', 'ssh_user', '')

        if ssh_host and ssh_user:
            # Download script from remote server and execute locally
            # 从远程服务器下载脚本并在本地执行
            return self._download_and_run_push_script()
        else:
            # Look for local script
            # 查找本地脚本
            script_path = "2-push_dualcambokeh_sdk.py"
            if os.path.exists(script_path):
                return self._run_local_push_script(script_path)
            else:
                self.logger.error(f"原始推送脚本不存在且未配置SSH / Original push script not found and SSH not configured: {script_path}")
                return False

    def _download_and_run_push_script(self) -> bool:
        """
        Download library from remote server and use local adb to push.
        从远程服务器下载库文件并使用本地adb推送。

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        if not PARAMIKO_AVAILABLE:
            self.logger.error("paramiko not available, cannot download from remote server / paramiko不可用，无法从远程服务器下载")
            return False

        try:
            # Connect to SSH server / 连接到SSH服务器
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Get SSH credentials / 获取SSH凭据
            ssh_host = self.config.get('device', 'ssh_host')
            ssh_user = self.config.get('device', 'ssh_user')
            ssh_port = self.config.getint('device', 'ssh_port', 22)
            ssh_password = self.config.get('device', 'ssh_password', '')
            ssh_key_file = self.config.get('device', 'ssh_key_file', '')

            # Connect using appropriate authentication / 使用适当的认证方式连接
            if ssh_key_file and os.path.exists(ssh_key_file):
                self.logger.info("使用SSH密钥认证 / Using SSH key authentication")
                ssh.connect(ssh_host, port=ssh_port, username=ssh_user, key_filename=ssh_key_file)
            elif ssh_password:
                self.logger.info("使用密码认证 / Using password authentication")
                ssh.connect(ssh_host, port=ssh_port, username=ssh_user, password=ssh_password)
            else:
                self.logger.info("尝试无密码认证 / Trying passwordless authentication")
                ssh.connect(ssh_host, port=ssh_port, username=ssh_user)

            # Download library file from remote server / 从远程服务器下载库文件
            success = self._download_library_via_ssh(ssh)
            ssh.close()

            if not success:
                return False

            # Now use internal logic to push the downloaded library / 现在使用内部逻辑推送下载的库
            self.logger.info("使用本地adb推送下载的库文件 / Using local adb to push downloaded library")
            return self._push_using_internal_logic()

        except Exception as e:
            self.logger.error(f"下载库文件并推送出错 / Error downloading library and pushing: {str(e)}")
            return False

    def _download_library_via_ssh(self, ssh) -> bool:
        """
        Download library file from remote server via SSH.
        通过SSH从远程服务器下载库文件。

        Args / 参数:
            ssh: SSH client connection / SSH客户端连接

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        try:
            # Get SDK path / 获取SDK路径
            sdk_path = self.config.get('build', 'sdk_path', '~/zx_code/bokeh/oppo_dualDepth/DualCamDepthEngine/jni/ArcBokehSim')

            # Remote library path / 远程库文件路径
            remote_lib_path = f"{sdk_path}/libs/{common.TARGET_ARCH_ABI}/{common.LIB_NAME}"

            # Local library path / 本地库文件路径
            local_lib_dir = os.path.join("libs", common.TARGET_ARCH_ABI)
            os.makedirs(local_lib_dir, exist_ok=True)
            local_lib_path = os.path.join(local_lib_dir, common.LIB_NAME)

            self.logger.info(f"下载库文件 / Downloading library: {remote_lib_path} -> {local_lib_path}")

            sftp = ssh.open_sftp()
            try:
                # Try to expand ~ manually / 尝试手动展开~
                if remote_lib_path.startswith('~'):
                    # Get home directory / 获取家目录
                    home_dir = sftp.normalize('.')
                    self.logger.info(f"SFTP家目录 / SFTP home directory: {home_dir}")

                    # Replace ~ with home directory / 用家目录替换~
                    expanded_path = remote_lib_path.replace('~', home_dir)
                    self.logger.info(f"展开后的库文件路径 / Expanded library path: {expanded_path}")
                    remote_lib_path = expanded_path

                # Check if remote library exists / 检查远程库文件是否存在
                sftp.stat(remote_lib_path)
                self.logger.info(f"远程库文件存在 / Remote library exists: {remote_lib_path}")

                # Download the library / 下载库文件
                sftp.get(remote_lib_path, local_lib_path)
                self.logger.info(f"库文件下载成功 / Library downloaded successfully: {local_lib_path}")

                # Check file size / 检查文件大小
                local_size = os.path.getsize(local_lib_path)
                self.logger.info(f"下载的库文件大小 / Downloaded library size: {local_size / (1024*1024):.2f} MB")

                sftp.close()
                return True

            except FileNotFoundError:
                self.logger.error(f"远程库文件不存在 / Remote library not found: {remote_lib_path}")
                sftp.close()
                return False
            except Exception as e:
                self.logger.error(f"下载库文件时出错 / Error downloading library: {str(e)}")
                sftp.close()
                return False

        except Exception as e:
            self.logger.error(f"SSH下载库文件出错 / Error during SSH library download: {str(e)}")
            return False

    def _run_local_push_script(self, script_path: str) -> bool:
        """
        Execute the push script locally on Windows.
        在Windows本地执行推送脚本。

        Args / 参数:
            script_path: Path to the push script / 推送脚本路径

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        if not os.path.exists(script_path):
            self.logger.error(f"推送脚本不存在 / Push script not found: {script_path}")
            return False

        try:
            self.logger.info(f"执行本地推送脚本 / Executing local push script: {script_path}")
            self.logger.info("将弹出独立的cmd窗口显示推送过程 / A separate cmd window will pop up to show push process")

            # Execute the push script in a new cmd window / 在新的cmd窗口中执行推送脚本
            import subprocess

            # Construct command with series parameter / 构造带有系列参数的命令
            script_cmd = f"python {script_path} -s {self.series}"

            # Use 'start' command to open a new cmd window / 使用'start'命令打开新的cmd窗口
            cmd_command = [
                'cmd', '/c', 'start', '/wait',
                'cmd', '/k',
                f'title Bokeh Push Script Execution && {script_cmd} && echo. && echo 推送脚本执行完成，按任意键关闭窗口... && pause && exit'
            ]

            # Run the command / 运行命令
            result = subprocess.run(cmd_command,
                                  cwd=os.getcwd(),
                                  creationflags=subprocess.CREATE_NEW_CONSOLE if hasattr(subprocess, 'CREATE_NEW_CONSOLE') else 0)

            if result.returncode != 0:
                self.logger.error(f"推送脚本执行失败，退出代码: {result.returncode} / Push script failed with exit code: {result.returncode}")
                return False

            self.logger.info("推送脚本执行完成 / Push script execution completed")
            return True

        except Exception as e:
            self.logger.error(f"执行推送脚本出错 / Error executing push script: {str(e)}")
            return False

    def _push_using_internal_logic(self) -> bool:
        """
        Push libraries using internal logic (original method).
        使用内部逻辑推送库（原有方法）。

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        timer = Timer()
        timer.start()

        self.logger.info("Pushing libraries to device... / 将库推送到设备...")

        # Prepare device / 准备设备
        self._prepare_device()

        # Push main library / 推送主库
        lib_full_path = os.path.join(self.lib_path, self.lib_name)
        if not os.path.exists(lib_full_path):
            self.logger.error(f"Library not found / 未找到库: {lib_full_path}")
            return False

        target_lib = os.path.join(self.target_lib_path, self.lib_name)
        self.logger.info(f"Pushing library / 推送库: {lib_full_path} -> {target_lib}")

        return_code, _, stderr = run_adb_command(
            f"push {lib_full_path} {target_lib}",
            device_id=self.device_id
        )

        if return_code != 0:
            self.logger.error(f"Failed to push library / 推送库失败: {stderr}")
            return False

        # Create target directory / 创建目标目录
        run_adb_command(
            f"shell mkdir -p {self.target_model_path}",
            device_id=self.device_id
        )

        # Get SDK path from config / 从配置获取SDK路径
        sdk_path = self.config.get('build', 'sdk_path', '.')

        # Push AI models / 推送AI模型
        model_file_path = os.path.join(
            sdk_path,
            common.THIRDPARTY_AIMODEL_PATH,
            f"{self.soc_config['TARGET_SOC']}_ODNN_{self.soc_config['ODNN_VERSION']}_{self.soc_config['AI_SDK']}_{self.soc_config['QNN_VERSION']}"
        )

        if os.path.exists(model_file_path):
            self.logger.info(f"Pushing AI models / 推送AI模型: {model_file_path} -> {self.target_model_path}")

            # Find and push all model files / 查找并推送所有模型文件
            all_model_files = common.find_all_files(model_file_path, [])
            for model_file in all_model_files:
                return_code, _, stderr = run_adb_command(
                    f"push {model_file} {self.target_model_path}/",
                    device_id=self.device_id
                )
                if return_code != 0:
                    self.logger.warning(f"Failed to push model file / 推送模型文件失败: {model_file}")
        else:
            self.logger.warning(f"Model path not found / 未找到模型路径: {model_file_path}")

        # Push CL binary / 推送CL二进制文件
        cl_binary_path = os.path.join(sdk_path, common.CL_BINARY_DIR, self.soc_config['CL_BINARY'])
        if os.path.exists(cl_binary_path):
            self.logger.info(f"Pushing CL binary / 推送CL二进制文件: {cl_binary_path}")
            return_code, _, stderr = run_adb_command(
                f"push {cl_binary_path} {self.target_model_path}/",
                device_id=self.device_id
            )
            if return_code != 0:
                self.logger.warning(f"Failed to push CL binary / 推送CL二进制文件失败: {stderr}")
        else:
            self.logger.warning(f"CL binary not found / 未找到CL二进制文件: {cl_binary_path}")

        # Push rectify params / 推送校正参数
        rectify_path = os.path.join(sdk_path, common.THIRDPARTY_RECTIFY_PATH, self.series)
        if os.path.exists(rectify_path):
            self.logger.info(f"Pushing rectify params / 推送校正参数: {rectify_path}")
            return_code, _, stderr = run_adb_command(
                f"push {rectify_path}/* {self.target_model_path}/",
                device_id=self.device_id
            )
            if return_code != 0:
                self.logger.warning(f"Failed to push rectify params / 推送校正参数失败: {stderr}")
        else:
            self.logger.warning(f"Rectify params not found / 未找到校正参数: {rectify_path}")

        # Restart camera service / 重启相机服务
        self.logger.info("Restarting camera service... / 重启相机服务...")
        run_adb_command(
            "shell pkill camera*",
            device_id=self.device_id
        )

        elapsed = timer.stop()
        self.logger.info(f"Libraries pushed successfully in {elapsed:.2f} seconds / 库在{elapsed:.2f}秒内成功推送")
        return True

    def push_test_files(self, test_case: Optional[str] = None) -> bool:
        """
        Push test files to the device.

        Args:
            test_case: Specific test case to push. If None, pushes all test cases.

        Returns:
            True if successful, False otherwise.
        """
        timer = Timer()
        timer.start()

        self.logger.info("Pushing test files to device...")

        # Create test directory on device
        run_adb_command(
            f"shell mkdir -p {self.test_program_path}",
            device_id=self.device_id
        )

        # Push test program
        test_program_full_path = os.path.join(self.lib_path, self.test_program)
        if not os.path.exists(test_program_full_path):
            self.logger.error(f"Test program not found: {test_program_full_path}")
            return False

        target_test_program = os.path.join(self.test_program_path, self.test_program)
        self.logger.info(f"Pushing {test_program_full_path} to {target_test_program}")

        return_code, _, stderr = run_adb_command(
            f"push {test_program_full_path} {target_test_program}",
            device_id=self.device_id
        )

        if return_code != 0:
            self.logger.error(f"Failed to push test program: {stderr}")
            return False

        # Make test program executable
        run_adb_command(
            f"shell chmod 777 {target_test_program}",
            device_id=self.device_id
        )

        # Push test case data
        if test_case:
            test_case_path = os.path.join('test_cases', test_case)
            if not os.path.exists(test_case_path):
                self.logger.error(f"Test case not found: {test_case_path}")
                return False

            self.logger.info(f"Pushing test case {test_case} to device...")

            # Create test case directory on device
            run_adb_command(
                f"shell mkdir -p {self.test_program_path}/SUCAI",
                device_id=self.device_id
            )

            # Push test case data
            return_code, _, stderr = run_adb_command(
                f"push {test_case_path}/* {self.test_program_path}/SUCAI/",
                device_id=self.device_id
            )

            if return_code != 0:
                self.logger.error(f"Failed to push test case data: {stderr}")
                return False

        elapsed = timer.stop()
        self.logger.info(f"Test files pushed successfully in {elapsed:.2f} seconds")
        return True

    def run_offline_test(self, test_case: Optional[str] = None) -> bool:
        """
        Run the offline test using remote script.
        使用远程脚本运行离线测试。

        Args / 参数:
            test_case: Specific test case to run / 要运行的特定测试用例

        Returns / 返回:
            True if successful, False otherwise / 如果成功则返回True，否则返回False
        """
        timer = Timer()
        timer.start()

        self.logger.info("运行离线测试 / Running offline test...")

        # 3-run_offline_test.bat脚本应该在Windows本地执行，不是在远程服务器上
        # The 3-run_offline_test.bat script should be executed locally on Windows, not on remote server
        success = self._run_local_test_script(test_case)

        elapsed = timer.stop()
        if success:
            self.logger.info(f"离线测试完成，耗时 {elapsed:.2f} 秒 / Offline test completed in {elapsed:.2f} seconds")
        else:
            self.logger.error(f"离线测试失败，耗时 {elapsed:.2f} 秒 / Offline test failed in {elapsed:.2f} seconds")

        return success

    def _run_local_test_script(self, test_case: Optional[str] = None) -> bool:
        """
        Execute 3-run_offline_test.bat script locally on Windows.
        在Windows本地执行3-run_offline_test.bat脚本。

        Args / 参数:
            test_case: Test case to run / 要运行的测试用例

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        self.logger.info("在Windows本地执行测试脚本 / Executing test script locally on Windows...")

        # 检查是否配置了SSH（需要从远程服务器获取脚本）
        # Check if SSH is configured (need to get script from remote server)
        ssh_host = self.config.get('device', 'ssh_host', '')
        ssh_user = self.config.get('device', 'ssh_user', '')

        if ssh_host and ssh_user:
            # 从远程服务器下载脚本到本地执行
            # Download script from remote server to execute locally
            return self._download_and_run_test_script()
        else:
            # 直接在本地执行脚本（如果脚本在本地）
            # Execute script directly if it's local
            return self._run_local_bat_script(test_case)

    def _download_and_run_test_script(self) -> bool:
        """
        Download test script from remote server and execute locally.
        从远程服务器下载测试脚本并在本地执行。

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        if not PARAMIKO_AVAILABLE:
            self.logger.error("paramiko not available, cannot download remote script / paramiko不可用，无法下载远程脚本")
            return False

        try:
            # Connect to SSH server / 连接到SSH服务器
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Get SSH credentials / 获取SSH凭据
            ssh_host = self.config.get('device', 'ssh_host')
            ssh_user = self.config.get('device', 'ssh_user')
            ssh_port = self.config.getint('device', 'ssh_port', 22)
            ssh_password = self.config.get('device', 'ssh_password', '')
            ssh_key_file = self.config.get('device', 'ssh_key_file', '')

            # Connect using appropriate authentication / 使用适当的认证方式连接
            if ssh_key_file and os.path.exists(ssh_key_file):
                self.logger.info("使用SSH密钥认证 / Using SSH key authentication")
                ssh.connect(ssh_host, port=ssh_port, username=ssh_user, key_filename=ssh_key_file)
            elif ssh_password:
                self.logger.info("使用密码认证 / Using password authentication")
                ssh.connect(ssh_host, port=ssh_port, username=ssh_user, password=ssh_password)
            else:
                self.logger.info("尝试无密码认证 / Trying passwordless authentication")
                ssh.connect(ssh_host, port=ssh_port, username=ssh_user)

            # Get SDK path / 获取SDK路径
            sdk_path = self.config.get('build', 'sdk_path', '~/zx_code/bokeh/oppo_dualDepth/DualCamDepthEngine/jni/ArcBokehSim')

            # For SFTP, use the original path with ~ (SFTP will expand it correctly)
            # 对于SFTP，使用原始的~路径（SFTP会正确展开）
            remote_script_path = f"{sdk_path}/3-run_offline_test.bat"
            local_script_path = "3-run_offline_test.bat"

            self.logger.info(f"下载测试脚本 / Downloading test script: {remote_script_path} -> {local_script_path}")

            sftp = ssh.open_sftp()
            try:
                # Check current working directory / 检查当前工作目录
                cwd = sftp.getcwd()
                self.logger.info(f"SFTP当前目录 / SFTP current directory: {cwd}")

                # Try to expand ~ manually / 尝试手动展开~
                if remote_script_path.startswith('~'):
                    # Get home directory / 获取家目录
                    home_dir = sftp.normalize('.')
                    self.logger.info(f"SFTP家目录 / SFTP home directory: {home_dir}")

                    # Replace ~ with home directory / 用家目录替换~
                    expanded_path = remote_script_path.replace('~', home_dir)
                    self.logger.info(f"展开后的路径 / Expanded path: {expanded_path}")
                    remote_script_path = expanded_path

                # Check if remote file exists / 检查远程文件是否存在
                sftp.stat(remote_script_path)
                self.logger.info(f"远程文件存在 / Remote file exists: {remote_script_path}")

                # Download the file / 下载文件
                sftp.get(remote_script_path, local_script_path)
                self.logger.info(f"文件下载成功 / File downloaded successfully: {local_script_path}")

            except FileNotFoundError:
                self.logger.error(f"远程文件不存在 / Remote file not found: {remote_script_path}")
                sftp.close()
                ssh.close()
                return False
            except Exception as e:
                self.logger.error(f"下载文件时出错 / Error downloading file: {str(e)}")
                sftp.close()
                ssh.close()
                return False

            sftp.close()
            ssh.close()

            # Execute the downloaded script locally / 在本地执行下载的脚本
            return self._run_local_bat_script()

        except Exception as e:
            self.logger.error(f"下载和执行远程脚本出错 / Error downloading and executing remote script: {str(e)}")
            return False

    def _run_local_bat_script(self, test_case: Optional[str] = None) -> bool:
        """
        Execute the .bat script locally on Windows.
        在Windows本地执行.bat脚本。

        Args / 参数:
            test_case: Test case to run / 要运行的测试用例

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        script_path = "3-run_offline_test.bat"

        if not os.path.exists(script_path):
            self.logger.error(f"测试脚本不存在 / Test script not found: {script_path}")
            return False

        try:
            self.logger.info(f"执行本地测试脚本 / Executing local test script: {script_path}")
            self.logger.info("将弹出独立的cmd窗口显示脚本执行过程 / A separate cmd window will pop up to show script execution")

            # Execute the batch script in a new cmd window / 在新的cmd窗口中执行批处理脚本
            import subprocess

            # Use 'start' command to open a new cmd window / 使用'start'命令打开新的cmd窗口
            # The /wait flag makes the parent process wait for the new window to close
            # /wait标志使父进程等待新窗口关闭
            cmd_command = [
                'cmd', '/c', 'start', '/wait',
                'cmd', '/k',
                f'title Bokeh Test Script Execution && {script_path} && echo. && echo 脚本执行完成，按任意键关闭窗口... && pause && exit'
            ]

            # Run the command / 运行命令
            result = subprocess.run(cmd_command,
                                  cwd=os.getcwd(),
                                  creationflags=subprocess.CREATE_NEW_CONSOLE if hasattr(subprocess, 'CREATE_NEW_CONSOLE') else 0)

            if result.returncode != 0:
                self.logger.error(f"测试脚本执行失败，退出代码: {result.returncode} / Test script failed with exit code: {result.returncode}")
                return False

            self.logger.info("测试脚本执行完成 / Test script execution completed")
            return True

        except Exception as e:
            self.logger.error(f"执行本地脚本出错 / Error executing local script: {str(e)}")
            return False

    def _run_remote_test_script(self) -> bool:
        """
        Execute 3-run_offline_test.bat script on remote server via SSH.
        通过SSH在远程服务器上执行3-run_offline_test.bat脚本。

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        if not PARAMIKO_AVAILABLE:
            self.logger.error("paramiko not available, cannot run remote test / paramiko不可用，无法运行远程测试")
            return False

        self.logger.info("通过SSH执行远程测试脚本 / Executing remote test script via SSH...")

        try:
            # Connect to SSH server / 连接到SSH服务器
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Get SSH credentials / 获取SSH凭据
            ssh_host = self.config.get('device', 'ssh_host')
            ssh_user = self.config.get('device', 'ssh_user')
            ssh_port = self.config.getint('device', 'ssh_port', 22)
            ssh_password = self.config.get('device', 'ssh_password', '')
            ssh_key_file = self.config.get('device', 'ssh_key_file', '')

            # Connect using appropriate authentication / 使用适当的认证方式连接
            if ssh_key_file and os.path.exists(ssh_key_file):
                self.logger.info("使用SSH密钥认证 / Using SSH key authentication")
                ssh.connect(ssh_host, port=ssh_port, username=ssh_user, key_filename=ssh_key_file)
            elif ssh_password:
                self.logger.info("使用密码认证 / Using password authentication")
                ssh.connect(ssh_host, port=ssh_port, username=ssh_user, password=ssh_password)
            else:
                self.logger.info("尝试无密码认证 / Trying passwordless authentication")
                ssh.connect(ssh_host, port=ssh_port, username=ssh_user)

            # Get SDK path / 获取SDK路径
            sdk_path = self.config.get('build', 'sdk_path', '~/zx_code/bokeh/oppo_dualDepth/DualCamDepthEngine/jni/ArcBokehSim')

            # Execute the test script (it's a .bat file, so run it directly) / 执行测试脚本（这是一个.bat文件，直接运行）
            test_script_cmd = f"cd {sdk_path} && ./3-run_offline_test.bat"
            self.logger.info(f"执行远程命令 / Executing remote command: {test_script_cmd}")

            stdin, stdout, stderr = ssh.exec_command(test_script_cmd)
            exit_status = stdout.channel.recv_exit_status()

            # Read output / 读取输出
            stdout_text = stdout.read().decode('utf-8')
            stderr_text = stderr.read().decode('utf-8')

            if exit_status != 0:
                self.logger.error(f"远程测试脚本执行失败，退出状态: {exit_status} / Remote test script failed with exit status: {exit_status}")
                self.logger.error(f"错误输出 / Error output: {stderr_text}")
                ssh.close()
                return False

            self.logger.info("远程测试脚本执行成功 / Remote test script executed successfully")
            self.logger.debug(f"脚本输出 / Script output: {stdout_text}")

            ssh.close()
            return True

        except Exception as e:
            self.logger.error(f"SSH远程执行出错 / Error during SSH remote execution: {str(e)}")
            return False

    def _run_local_test(self, test_case: Optional[str] = None) -> bool:
        """
        Run test locally on the device (original logic).
        在设备上本地运行测试（原有逻辑）。

        Args / 参数:
            test_case: Test case to run / 要运行的测试用例

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        self.logger.info("在设备上本地运行测试 / Running test locally on device...")

        # Set up CPU and GPU frequencies for consistent performance
        self._setup_performance()

        # Create result directory on device
        run_adb_command(
            f"shell mkdir -p {self.test_program_path}/RESULT",
            device_id=self.device_id
        )

        # Run the test
        test_cmd = f"shell \"cd {self.test_program_path} && ./test_arcbokehsim SUCAI 1\""
        self.logger.info(f"运行测试命令 / Running test command: {test_cmd}")

        return_code, stdout, stderr = run_adb_command(
            test_cmd,
            device_id=self.device_id
        )

        if return_code != 0:
            self.logger.error(f"本地测试失败 / Local test failed: {stderr}")
            return False

        self.logger.debug(f"测试输出 / Test output: {stdout}")
        return True

    def pull_results(self, output_dir: str) -> str:
        """
        Pull test results and dump data from the device.
        从设备拉取测试结果和dump数据。

        Args / 参数:
            output_dir: Directory to store the results / 存储结果的目录

        Returns / 返回:
            Path to the pulled results / 拉取结果的路径
        """
        timer = Timer()
        timer.start()

        self.logger.info(f"Pulling test results to {output_dir}...")

        # Create result directory / 创建结果目录
        result_dir = os.path.join(output_dir, 'results')
        ensure_directory(result_dir)

        # Pull main test results (大模块结果在服务器上，这里主要是测试程序的输出)
        # Pull main test results (main module results are on server, this is mainly test program output)
        device_result_path = f"{self.test_program_path}/RESULT"
        self.logger.info(f"Pulling {device_result_path} to {result_dir}")

        return_code, _, stderr = run_adb_command(
            f"pull {device_result_path} {result_dir}",
            device_id=self.device_id
        )

        if return_code != 0:
            self.logger.error(f"Failed to pull results: {stderr}")

        # Pull sub-module dump data from correct path / 从正确路径拉取子模块dump数据
        # 子模块dump路径：/sdcard/Android/data/com.oplus.camera/files/spdebug/bokehdump
        sub_module_dump_path = "/sdcard/Android/data/com.oplus.camera/files/spdebug/bokehdump"
        dump_dir = os.path.join(output_dir, 'sub_module_dump')
        ensure_directory(dump_dir)

        self.logger.info(f"Pulling dump data from {sub_module_dump_path} to {dump_dir}")

        return_code, _, stderr = run_adb_command(
            f"pull {sub_module_dump_path} {dump_dir}",
            device_id=self.device_id
        )

        if return_code != 0:
            self.logger.warning(f"Failed to pull sub-module dump data (may be empty): {stderr}")
        else:
            self.logger.info("Sub-module dump data pulled successfully / 子模块dump数据拉取成功")

        elapsed = timer.stop()
        self.logger.info(f"Results pulled successfully in {elapsed:.2f} seconds")
        return result_dir

    def _prepare_device(self):
        """Prepare the device for pushing libraries."""
        self.logger.info("Preparing device...")

        # Root and remount
        run_adb_command("root", device_id=self.device_id)
        time.sleep(1)  # Wait for root to take effect
        run_adb_command("remount", device_id=self.device_id)

        # Disable SELinux
        run_adb_command("shell setenforce 0", device_id=self.device_id)

        # Stop camera app
        run_adb_command("shell am force-stop com.oplus.camera", device_id=self.device_id)

    def _setup_performance(self):
        """Set up CPU and GPU frequencies for consistent performance."""
        self.logger.info("Setting up performance parameters...")

        # Set GPU frequency
        run_adb_command("shell \"echo 1 > /sys/class/kgsl/kgsl-3d0/force_rail_on\"", device_id=self.device_id)
        run_adb_command("shell \"echo 1 > /sys/class/kgsl/kgsl-3d0/force_clk_on\"", device_id=self.device_id)
        run_adb_command("shell \"echo 1 > /sys/class/kgsl/kgsl-3d0/force_bus_on\"", device_id=self.device_id)
        run_adb_command("shell \"echo 10000000 > /sys/class/kgsl/kgsl-3d0/idle_timer\"", device_id=self.device_id)
        run_adb_command("shell \"echo 3 > /sys/class/kgsl/kgsl-3d0/min_pwrlevel\"", device_id=self.device_id)
        run_adb_command("shell \"echo 3 > /sys/class/kgsl/kgsl-3d0/max_pwrlevel\"", device_id=self.device_id)
        run_adb_command("shell \"echo performance > /sys/class/kgsl/kgsl-3d0/devfreq/governor\"", device_id=self.device_id)
