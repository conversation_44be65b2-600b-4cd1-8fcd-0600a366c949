# Bokeh验证工具使用指南

## 概述

Bokeh验证工具支持两种工作模式：

1. **本地模式**：在Windows本地运行构建（需要NDK环境）
2. **SSH远程模式**：通过SSH连接到Linux服务器进行构建（推荐）

## 模式1：SSH远程构建（推荐）

### 前提条件
- Windows本地环境
- 可访问的Linux服务器（包含Bokeh项目）
- SSH连接权限
- 连接的Android设备

### 配置步骤

1. **安装依赖**：
   ```bash
   pip install paramiko pillow
   ```

2. **创建SSH配置**：
   ```bash
   cp config.ssh.ini.example config.ssh.ini
   ```

3. **编辑SSH配置**（`config.ssh.ini`）：
   ```ini
   [device]
   series = X8U
   ssh_host = your.linux.server.com
   ssh_user = your_username
   ssh_password = your_password
   # 或使用SSH密钥
   # ssh_key_file = ~/.ssh/id_rsa

   [build]
   sdk_path = /path/to/your/bokeh/project
   ```

4. **测试SSH连接**：
   ```bash
   python test_ssh_connection.py
   ```

5. **运行验证工具**：
   ```bash
   # 自动模式：SSH构建 -> 下载 -> 推送
   python bokeh_verify.py --config config.ssh.ini

   # 查看详细输出
   python bokeh_verify.py --config config.ssh.ini --verbose

   # 清理构建
   python bokeh_verify.py --config config.ssh.ini --clean
   ```

### SSH模式工作流程

1. **SSH连接**：连接到Linux服务器
2. **远程构建**：在服务器上运行`1-build_dualcambokeh_sdk.py`
3. **下载产物**：将构建好的库文件下载到本地
4. **推送到设备**：将库文件推送到Android设备
5. **验证测试**：运行离线测试和比较

## 模式2：本地构建

### 前提条件
- Windows本地环境
- Android NDK环境
- Bokeh项目代码在本地
- 连接的Android设备

### 配置步骤

1. **配置NDK环境**：
   - 安装Android NDK
   - 将`ndk-build`添加到PATH环境变量

2. **使用本地配置**：
   ```bash
   # 使用默认配置
   python bokeh_verify.py

   # 或指定本地配置
   python bokeh_verify.py --config config.simple.ini
   ```

## 常用命令

### 基本使用
```bash
# 最简单的使用方式
python bokeh_verify.py

# 使用SSH配置
python bokeh_verify.py --config config.ssh.ini

# 查看帮助
python bokeh_verify.py --help
```

### 分步执行
```bash
# 只构建
python bokeh_verify.py --mode build

# 只推送
python bokeh_verify.py --mode push

# 只验证
python bokeh_verify.py --mode verify

# 完整流程
python bokeh_verify.py --mode full
```

### 高级选项
```bash
# 跳过构建步骤
python bokeh_verify.py --no-build

# 跳过推送步骤
python bokeh_verify.py --no-push

# 清理构建
python bokeh_verify.py --clean

# 详细输出
python bokeh_verify.py --verbose

# 指定输出目录
python bokeh_verify.py --output-dir my_results
```

## 测试和调试

### 测试脚本
```bash
# 测试SSH连接
python test_ssh_connection.py

# 测试构建和推送
python test_build_push.py

# 简单的构建推送（无复杂依赖）
python simple_build_push.py
```

### 调试技巧

1. **使用详细输出**：
   ```bash
   python bokeh_verify.py --verbose
   ```

2. **检查配置**：
   ```bash
   # 测试SSH连接
   python test_ssh_connection.py
   
   # 检查设备连接
   adb devices
   ```

3. **分步调试**：
   ```bash
   # 先测试构建
   python bokeh_verify.py --mode build --verbose
   
   # 再测试推送
   python bokeh_verify.py --mode push --verbose
   ```

## 常见问题

### SSH相关问题

**问题**：SSH连接失败
**解决**：
- 检查网络连接
- 验证SSH凭据
- 确认服务器地址和端口

**问题**：SSH认证失败
**解决**：
- 检查用户名和密码
- 使用SSH密钥认证
- 确认服务器SSH配置

### 构建相关问题

**问题**：构建脚本未找到
**解决**：
- 确认`sdk_path`配置正确
- 检查`1-build_dualcambokeh_sdk.py`是否存在

**问题**：NDK未找到（本地模式）
**解决**：
- 安装Android NDK
- 将ndk-build添加到PATH

### 设备相关问题

**问题**：设备未找到
**解决**：
- 检查USB连接
- 启用USB调试
- 运行`adb devices`确认

**问题**：推送失败
**解决**：
- 确认设备已root
- 运行`adb remount`
- 检查设备权限

## 输出说明

### 成功输出示例
```
🚀 Bokeh Verification Tool / Bokeh验证工具
============================================================
📁 Output directory / 输出目录: verify_results/bokeh_verify_20241201_143022
🔄 Auto mode: Build -> Check -> Push -> Verify
🔄 自动模式：构建 -> 检查 -> 推送 -> 验证
🔨 Step 1: Building Bokeh SDK... / 步骤1：构建Bokeh SDK...
📥 Downloading build artifacts... / 下载构建产物...
✅ Download successful: libs/arm64-v8a/libOPAlgoCamCaptureDualPortrait.so (15.2 MB)
✅ Library found / 找到库文件: libs/arm64-v8a/libOPAlgoCamCaptureDualPortrait.so (15.2 MB)
📱 Step 2: Pushing to device... / 步骤2：推送到设备...
✅ Libraries pushed successfully in 12.34 seconds / 库在12.34秒内成功推送
✅ Build and Push completed successfully! / 构建和推送成功完成！
⏱️  Completed in 45.67 seconds / 在 45.67 秒内完成
```

### 错误输出示例
```
❌ SSH connection failed / SSH连接失败
❌ Build failed, stopping auto mode / 构建失败，停止自动模式
❌ Library not found, please build first / 未找到库文件，请先构建
❌ No device connected / 未连接设备
```

## 下一步

成功完成构建和推送后，您可以：

1. **运行验证测试**：
   ```bash
   python bokeh_verify.py --mode verify
   ```

2. **添加测试用例**：
   在`test_cases/`目录下添加测试数据

3. **配置dump设置**：
   编辑配置文件中的dump相关设置

4. **查看验证报告**：
   检查`verify_results/`目录中的HTML报告
