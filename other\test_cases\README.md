# Test Cases

This directory contains test cases for the Bokeh verification tool.

## Structure

Each test case should be in its own directory with the following structure:

```
test_case_name/
├── config.json       # Test case configuration
├── input/            # Input images and data
└── expected/         # Expected output (golden data)
```

## Configuration

The `config.json` file should contain:

```json
{
  "name": "Test Case Name",
  "description": "Description of the test case",
  "modules": ["depth", "render", "rectify"],
  "thresholds": {
    "depth": 5.0,
    "render": 5.0,
    "rectify": 5.0,
    "final": 3.0
  }
}
```

## Adding New Test Cases

1. Create a new directory with a descriptive name
2. Add input images to the `input` directory
3. Add expected output to the `expected` directory
4. Create a `config.json` file with appropriate settings
