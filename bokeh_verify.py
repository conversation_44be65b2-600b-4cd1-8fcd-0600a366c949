#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bokeh Verification Tool / Bokeh 验证工具

This tool automates the verification process for Bokeh algorithm changes by comparing
output results with previous versions.
该工具通过比较输出结果来自动化验证Bokeh算法更改的过程。

Usage / 使用方法:
    python bokeh_verify.py --mode [verify|build|push|full] [options]

Author / 作者: <EMAIL>
Date / 日期: 2025.5.27
"""

import os
import sys
import time
import argparse
import logging
from datetime import datetime

# Add src directory to path / 将src目录添加到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import modules / 导入模块
from config import Config
from build import BokehBuilder
from push import DevicePusher
from dump import DumpManager
from compare import ResultComparator
from report import ReportGenerator
from golden import GoldenDataManager
from utils import setup_logging, Timer
import common

def parse_arguments():
    """
    Parse command line arguments.
    解析命令行参数。
    """
    parser = argparse.ArgumentParser(description='Bokeh Verification Tool / Bokeh 验证工具')

    # Main operation mode / 主要操作模式
    parser.add_argument('--mode', type=str, choices=['verify', 'build', 'push', 'full', 'auto'],
                        default='auto', help='Operation mode (default: auto) / 操作模式（默认：auto自动模式）')

    # Build options / 构建选项
    parser.add_argument('-c', '--clean', action='store_true', help='Clean build / 清理构建')
    parser.add_argument('--no-build', action='store_true', help='Skip build step / 跳过构建步骤')
    parser.add_argument('--no-push', action='store_true', help='Skip push step / 跳过推送步骤')
    parser.add_argument('--no-verify', action='store_true', help='Skip verification step / 跳过验证步骤')
    parser.add_argument('--use-original-push', action='store_true', help='Use original 2-push script with CMD window / 使用原始2-push脚本并显示CMD窗口')

    # Test case options / 测试用例选项
    parser.add_argument('--case', type=str, help='Specific test case to run / 要运行的特定测试用例')

    # Dump options / Dump选项
    parser.add_argument('--dump-modules', type=str, nargs='+',
                        choices=['depth', 'rectify', 'render', 'depth_warp', 'all'],
                        help='Specific modules to dump: depth(depthcap), rectify(rectify.dump), render, depth_warp, all / 要dump的特定模块：depth(depthcap), rectify(rectify.dump), render, depth_warp, all')

    # Golden data options / Golden数据选项
    parser.add_argument('--save-golden', type=str, nargs='?', const='auto',
                        help='Save current results as golden data with optional version name / 将当前结果保存为golden数据，可选版本名称')
    parser.add_argument('--list-golden', action='store_true',
                        help='List all available golden data versions / 列出所有可用的golden数据版本')
    parser.add_argument('--load-golden', type=str,
                        help='Load specific golden data version as current / 加载特定golden数据版本作为当前版本')

    # Output options / 输出选项
    parser.add_argument('--output-dir', type=str, help='Output directory for results / 结果的输出目录')

    # Misc options / 其他选项
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output / 详细输出')
    parser.add_argument('--config', type=str, default='config.simple.ini',
                        help='Path to config file / 配置文件的路径 (default: config.simple.ini)')

    return parser.parse_args()

def check_library_exists(lib_path: str, logger) -> bool:
    """
    Check if the library file exists and is valid.
    检查库文件是否存在且有效。

    Args / 参数:
        lib_path: Path to the library file / 库文件路径
        logger: Logger instance / 日志实例

    Returns / 返回:
        True if library exists and is valid / 如果库存在且有效则返回True
    """
    if not os.path.exists(lib_path):
        logger.error(f"❌ Library not found / 未找到库文件: {lib_path}")
        return False

    file_size = os.path.getsize(lib_path)
    if file_size == 0:
        logger.error(f"❌ Library file is empty / 库文件为空: {lib_path}")
        return False

    logger.info(f"✅ Library found / 找到库文件: {lib_path} ({file_size / (1024*1024):.1f} MB)")
    return True

def main():
    """
    Main entry point for the Bokeh verification tool.
    Bokeh验证工具的主入口点。
    """
    # Parse arguments / 解析参数
    args = parse_arguments()

    # Setup logging / 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    setup_logging(log_level)
    logger = logging.getLogger('bokeh_verify')

    # Print banner / 打印横幅
    logger.info("🚀 Bokeh Verification Tool / Bokeh验证工具")
    logger.info("=" * 60)

    # Start timer / 启动计时器
    timer = Timer()
    timer.start()

    # Load configuration / 加载配置
    config = Config(args.config)

    # Override config with command line arguments / 用命令行参数覆盖配置
    if args.output_dir:
        config.set('output', 'dir', args.output_dir)

    # Create timestamp for this run / 为本次运行创建时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    run_id = f"bokeh_verify_{timestamp}"

    # Set up output directory / 设置输出目录
    output_dir = os.path.join(config.get('output', 'dir'), run_id)
    os.makedirs(output_dir, exist_ok=True)
    logger.info(f"📁 Output directory / 输出目录: {output_dir}")

    # Initialize components / 初始化组件
    builder = BokehBuilder(config)
    pusher = DevicePusher(config)
    dump_manager = DumpManager(config)
    comparator = ResultComparator(config)
    report_generator = ReportGenerator(config)
    golden_manager = GoldenDataManager(config)

    try:
        # Auto mode: build -> check -> push -> verify / 自动模式：构建 -> 检查 -> 推送 -> 验证
        if args.mode == 'auto':
            logger.info("🔄 Auto mode: Build -> Check -> Push -> Verify")
            logger.info("🔄 自动模式：构建 -> 检查 -> 推送 -> 验证")

            # Step 1: Build (if not skipped) / 步骤1：构建（如果未跳过）
            if not args.no_build and config.getboolean('build', 'auto_build', True):
                logger.info("🔨 Step 1: Building Bokeh SDK... / 步骤1：构建Bokeh SDK...")
                build_success = builder.build(clean=args.clean)

                if not build_success:
                    logger.error("❌ Build failed, stopping auto mode / 构建失败，停止自动模式")
                    return 1

                # For SSH builds, library is ready on remote server / 对于SSH构建，库文件已在远程服务器准备就绪
                # For local builds, check the build output / 对于本地构建，检查构建输出
                if config.get('device', 'ssh_host') and config.get('device', 'ssh_user'):
                    logger.info("✅ SSH build completed, library ready on remote server / SSH构建完成，库文件已在远程服务器准备就绪")
                else:
                    # For local builds, check the build output / 对于本地构建，检查构建输出
                    lib_path = builder.get_lib_path()
                    if not check_library_exists(lib_path, logger):
                        logger.error("❌ Library check failed, stopping auto mode / 库检查失败，停止自动模式")
                        return 1
            else:
                logger.info("⏭️  Skipping build step / 跳过构建步骤")
                # Still check if library exists / 仍然检查库是否存在
                if config.get('device', 'ssh_host') and config.get('device', 'ssh_user'):
                    # For SSH builds, check local downloaded library / 对于SSH构建，检查本地下载的库
                    lib_path = os.path.join("libs", common.TARGET_ARCH_ABI, common.LIB_NAME)
                else:
                    # For local builds, check the build output / 对于本地构建，检查构建输出
                    lib_path = builder.get_lib_path()

                if not check_library_exists(lib_path, logger):
                    logger.error("❌ Library not found, please build first / 未找到库文件，请先构建")
                    return 1

            # Step 2: Push (if not skipped) / 步骤2：推送（如果未跳过）
            if not args.no_push and config.getboolean('build', 'auto_push', True):
                logger.info("📱 Step 2: Pushing to device... / 步骤2：推送到设备...")

                # Use original push script if requested / 如果请求则使用原始推送脚本
                use_original = args.use_original_push
                if use_original:
                    logger.info("🔧 Using original 2-push script / 使用原始2-push脚本")

                push_success = pusher.push_libraries(use_original_script=use_original)

                if not push_success:
                    logger.error("❌ Push failed, stopping auto mode / 推送失败，停止自动模式")
                    return 1

                # Push test files if case is specified / 如果指定了测试用例则推送测试文件
                if args.case:
                    pusher.push_test_files(args.case)
            else:
                logger.info("⏭️  Skipping push step / 跳过推送步骤")

            # Step 3: Verify (if not skipped) / 步骤3：验证（如果未跳过）
            if not args.no_verify:
                logger.info("🧪 Step 3: Running verification... / 步骤3：运行验证...")

                # Configure dump settings / 配置dump设置
                logger.info("⚙️  Configuring dump settings... / 配置dump设置...")
                if args.dump_modules:
                    for module in args.dump_modules:
                        dump_manager.enable_module_dump(module)
                else:
                    dump_manager.enable_default_dumps()

                # Run the test / 运行测试
                logger.info("🧪 Running offline test... / 运行离线测试...")
                test_success = pusher.run_offline_test(args.case)

                if not test_success:
                    logger.error("❌ Test execution failed, stopping auto mode / 测试执行失败，停止自动模式")
                    return 1

                # Pull results / 拉取结果
                logger.info("📥 Pulling results from device... / 从设备拉取结果...")
                result_path = pusher.pull_results(output_dir)

                logger.info("✅ Verification completed successfully! / 验证成功完成！")
                logger.info(f"📁 Results saved to / 结果保存到: {output_dir}")
            else:
                logger.info("⏭️  Skipping verification step / 跳过验证步骤")
                logger.info("✅ Build and Push completed successfully! / 构建和推送成功完成！")
                logger.info("📋 Next: You can run verification with --mode verify")
                logger.info("📋 下一步：您可以使用 --mode verify 运行验证")

        # Execute based on mode / 根据模式执行
        elif args.mode in ['build', 'full']:
            logger.info("🔨 Building Bokeh SDK... / 构建Bokeh SDK...")
            build_success = builder.build(clean=args.clean)
            if build_success:
                # Check library exists / 检查库文件是否存在
                if config.get('device', 'ssh_host') and config.get('device', 'ssh_user'):
                    # For SSH builds, check local downloaded library / 对于SSH构建，检查本地下载的库
                    lib_path = os.path.join("libs", common.TARGET_ARCH_ABI, common.LIB_NAME)
                else:
                    # For local builds, check the build output / 对于本地构建，检查构建输出
                    lib_path = builder.get_lib_path()
                check_library_exists(lib_path, logger)

        if args.mode in ['push', 'full']:
            logger.info("📱 Pushing to device... / 推送到设备...")

            # Use original push script if requested / 如果请求则使用原始推送脚本
            use_original = args.use_original_push
            if use_original:
                logger.info("🔧 Using original 2-push script / 使用原始2-push脚本")

            pusher.push_libraries(use_original_script=use_original)
            if args.case:
                pusher.push_test_files(args.case)

        if args.mode in ['verify', 'full']:
            # Configure dump settings / 配置dump设置
            logger.info("⚙️  Configuring dump settings... / 配置dump设置...")
            if args.dump_modules:
                for module in args.dump_modules:
                    dump_manager.enable_module_dump(module)
            else:
                dump_manager.enable_default_dumps()

            # Run the test / 运行测试
            logger.info("🧪 Running offline test... / 运行离线测试...")
            pusher.run_offline_test(args.case)

            # Pull results / 拉取结果
            logger.info("📥 Pulling results from device... / 从设备拉取结果...")
            result_path = pusher.pull_results(output_dir)

            # Compare results / 比较结果
            logger.info("🔍 Comparing results... / 比较结果...")
            reference_path = config.get('compare', 'reference_dir')
            diff_results = comparator.compare(result_path, reference_path)

            # Generate report / 生成报告
            logger.info("📊 Generating report... / 生成报告...")
            report_path = report_generator.generate(
                diff_results,
                output_dir,
                format='html'
            )

            # Display summary / 显示摘要
            if diff_results['pass']:
                logger.info("✅ Verification PASSED! / 验证通过！")
            else:
                logger.warning("❌ Verification FAILED! / 验证失败！")
                logger.warning(f"📄 See report at / 查看报告: {report_path}")

        # Log completion time / 记录完成时间
        elapsed = timer.elapsed()
        logger.info(f"⏱️  Completed in {elapsed:.2f} seconds / 在 {elapsed:.2f} 秒内完成")

    except Exception as e:
        logger.error(f"💥 Error / 错误: {str(e)}", exc_info=True)
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
