# Bokeh Verification Tool Demo 1.0

## 🚀 简介 / Introduction

Bokeh验证工具Demo 1.0版本，提供简化的Bokeh双摄虚化算法验证功能。

This is Demo 1.0 of the Bokeh Verification Tool, providing simplified verification functionality for Bokeh dual-camera blur algorithms.

## ✨ 主要功能 / Key Features

- **🔨 SSH远程构建**：在远程Linux服务器上构建Bokeh SDK
- **📱 自动推送库**：使用原始推送脚本将库文件推送到设备
- **🧪 CMD窗口测试**：在独立CMD窗口中执行离线测试脚本
- **📊 Dump数据管理**：支持主要结果和子模块数据dump
- **🧹 历史数据清理**：子模块dump前自动清理历史数据

## 🎯 简化的使用方法 / Simplified Usage

### 基本使用 / Basic Usage

```bash
# 默认模式：跳过构建 + 推送库 + 运行测试 + 只dump主要结果
python bokeh_verify.py

# 启用详细输出
python bokeh_verify.py -v

# 启用子模块dump（会自动清理历史数据）
python bokeh_verify.py -dp

# 启用构建
python bokeh_verify.py --build
```

### 参数说明 / Parameters

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--config` | 配置文件路径 | `config.ssh.ini` |
| `-v, --verbose` | 启用详细输出 | 关闭 |
| `--build` | 启用构建步骤 | 跳过 |
| `-dp, --dump-sub-modules` | 启用子模块dump | 关闭 |
| `--case` | 指定测试用例 | 全部 |

## 📁 文件结构 / File Structure

```
demo1.0/
├── bokeh_verify.py          # 主程序
├── src/                     # 源代码模块
│   ├── build.py            # 构建模块
│   ├── push.py             # 推送模块
│   ├── dump.py             # Dump管理模块
│   ├── config.py           # 配置管理模块
│   ├── utils.py            # 工具函数
│   └── ...
├── config.ssh.ini          # SSH配置文件
├── config.template.ini     # 配置模板
├── requirements.txt        # Python依赖
├── libs/                   # 库文件目录
├── verify_results/         # 验证结果目录
└── README.md              # 说明文档
```

## 📊 输出结果 / Output Results

验证结果统一保存在`verify_results/`目录下，按时间戳组织：

```
verify_results/
└── bokeh_verify_YYYYMMDD_HHMMSS/
    ├── results/                    # 主要测试结果
    │   └── RESULT/                # 最终输出图像
    │       ├── *_1024x768.png     # 原始图像
    │       ├── *_final.png        # 最终输出图像
    │       └── *_bokehRes_*.png   # Bokeh效果图像
    └── sub_module_dump/           # 子模块dump数据（使用-dp时）
        └── bokehdump/             # 各模块中间处理结果
            ├── *_rectify_*.png    # 校正模块输出
            ├── *_depth_*.png      # 深度模块输出
            ├── *_AIDET_*.png      # AI检测结果
            ├── *_AISegmentation_*.png # AI分割结果
            ├── *_render_*.png     # 渲染模块输出
            └── *.txt              # 参数和决策数据
```

## ⚙️ 配置 / Configuration

1. **复制配置模板**：
   ```bash
   copy config.template.ini config.ssh.ini
   ```

2. **修改SSH配置**：
   ```ini
   [device]
   ssh_host = *************
   ssh_user = 80398453
   ssh_port = 22
   ```

3. **设置SDK路径**：
   ```ini
   [build]
   sdk_path = ~/zx_code/bokeh/oppo_dualDepth/DualCamDepthEngine/jni/ArcBokehSim
   ```

## 🔄 工作流程 / Workflow

1. **构建（可选）**：SSH远程构建Bokeh SDK
2. **推送库**：下载并推送库文件到设备
3. **配置Dump**：设置dump开关和路径
4. **运行测试**：在CMD窗口中执行离线测试
5. **拉取结果**：下载测试结果和dump数据

## 🛠️ 依赖要求 / Requirements

- Python 3.7+
- paramiko (SSH连接)
- ADB (Android Debug Bridge)
- Windows环境

## 📝 版本信息 / Version Info

- **版本**：Demo 1.0
- **日期**：2025-05-30
- **状态**：稳定版本

## 🎉 主要改进 / Key Improvements

相比原始版本的改进：

1. **参数大幅简化**：从10+个参数减少到4个核心参数
2. **默认行为优化**：符合日常使用习惯
3. **代码结构清晰**：main函数简化，步骤函数化
4. **历史数据清理**：子模块dump前自动清理
5. **执行效率提升**：测试时间大幅缩短
6. **结果目录统一**：所有结果保存在verify_results目录下

## 📞 支持 / Support

如有问题，请联系开发团队。

For support, please contact the development team.
