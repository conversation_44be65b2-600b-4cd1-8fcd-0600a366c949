#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration module for Bokeh Verification Tool.

This module handles loading, parsing, and accessing configuration settings.
"""

import os
import configparser
import logging
from typing import Any, Dict, Optional

class Config:
    """Configuration manager for Bokeh Verification Tool."""
    
    def __init__(self, config_path: str = 'config.ini'):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Path to the configuration file.
        """
        self.logger = logging.getLogger('bokeh_verify.config')
        self.config_path = config_path
        self.config = configparser.ConfigParser()
        
        # Load default configuration
        self._load_defaults()
        
        # Load user configuration if exists
        if os.path.exists(config_path):
            self.logger.info(f"Loading configuration from {config_path}")
            self.config.read(config_path)
        else:
            self.logger.warning(f"Configuration file {config_path} not found, using defaults")
            # Create default config file
            self.save()
    
    def _load_defaults(self):
        """Load default configuration settings."""
        self.config['device'] = {
            'series': 'X8U',
            'id': '',  # Will be auto-detected if empty
            'ssh_host': '',
            'ssh_user': '',
            'ssh_port': '22',
        }
        
        self.config['build'] = {
            'sdk_path': '',
            'build_script': 'build.py',
            'build_args': 'debug',
            'output_path': 'libs',
        }
        
        self.config['push'] = {
            'lib_path': 'libs/arm64-v8a',
            'lib_name': 'libOPAlgoCamCaptureDualPortrait.so',
            'target_lib_path': '/odm/lib64',
            'test_program': 'test_arcbokehsim',
            'test_program_path': '/data/local/tmp/BokehSim',
        }
        
        self.config['dump'] = {
            'dump_base_path': '/sdcard/Android/data/com.oplus.camera/files/spdebug/bokehdump',
            'dump_local_path': 'outputs/vbdump',
            'depth_dump_level': '3',
            'render_dump_level': '3',
            'rectify_dump_level': '3',
            'depth_warp_dump_level': '3',
            'create_new_dir': '0',
        }
        
        self.config['compare'] = {
            'reference_dir': 'verify_data/golden',
            'global_threshold': '5',
            'module_thresholds': {
                'depth': '5',
                'render': '5',
                'rectify': '5',
                'final': '3',
            },
        }
        
        self.config['output'] = {
            'dir': 'verify_results',
            'save_all_images': 'True',
            'save_diff_only': 'False',
        }
    
    def get(self, section: str, option: str, fallback: Any = None) -> str:
        """
        Get a configuration value.
        
        Args:
            section: Configuration section.
            option: Configuration option.
            fallback: Fallback value if not found.
            
        Returns:
            Configuration value as string.
        """
        return self.config.get(section, option, fallback=fallback)
    
    def getint(self, section: str, option: str, fallback: Optional[int] = None) -> int:
        """Get a configuration value as integer."""
        return self.config.getint(section, option, fallback=fallback)
    
    def getfloat(self, section: str, option: str, fallback: Optional[float] = None) -> float:
        """Get a configuration value as float."""
        return self.config.getfloat(section, option, fallback=fallback)
    
    def getboolean(self, section: str, option: str, fallback: Optional[bool] = None) -> bool:
        """Get a configuration value as boolean."""
        return self.config.getboolean(section, option, fallback=fallback)
    
    def set(self, section: str, option: str, value: str):
        """
        Set a configuration value.
        
        Args:
            section: Configuration section.
            option: Configuration option.
            value: New value.
        """
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, option, value)
    
    def save(self, path: Optional[str] = None):
        """
        Save the configuration to a file.
        
        Args:
            path: Path to save the configuration file. If None, uses the original path.
        """
        save_path = path if path else self.config_path
        with open(save_path, 'w') as f:
            self.config.write(f)
        self.logger.info(f"Configuration saved to {save_path}")
    
    def get_module_thresholds(self) -> Dict[str, float]:
        """
        Get module-specific comparison thresholds.
        
        Returns:
            Dictionary mapping module names to threshold values.
        """
        thresholds = {}
        if self.config.has_section('compare'):
            for key, value in self.config.items('compare'):
                if key.startswith('module_threshold_'):
                    module = key.replace('module_threshold_', '')
                    thresholds[module] = float(value)
        return thresholds
