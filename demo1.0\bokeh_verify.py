#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bokeh Verification Tool / Bokeh 验证工具

This tool automates the verification process for Bokeh algorithm changes by comparing
output results with previous versions.
该工具通过比较输出结果来自动化验证Bokeh算法更改的过程。

Usage / 使用方法:
    python bokeh_verify.py --mode [verify|build|push|full] [options]

Author / 作者: <EMAIL>
Date / 日期: 2025.5.27
"""

import os
import sys
import time
import argparse
import logging
from datetime import datetime

# Add src directory to path / 将src目录添加到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import modules / 导入模块
from config import Config
from build import BokehBuilder
from push import DevicePusher
from dump import DumpManager
from compare import ResultComparator
from report import ReportGenerator
from golden import GoldenDataManager
from utils import setup_logging, Timer
import common

def parse_arguments():
    """
    Parse command line arguments.
    解析命令行参数。
    """
    parser = argparse.ArgumentParser(description='Bokeh Verification Tool / Bokeh 验证工具')

    # 简化的基本选项 / Simplified basic options
    parser.add_argument('--config', type=str, default='config.ssh.ini',
                        help='Configuration file path / 配置文件路径 (默认: config.ssh.ini)')
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output / 启用详细输出')

    # 简化的操作选项 / Simplified operation options
    parser.add_argument('--build', action='store_true', help='Enable build step / 启用构建步骤 (默认跳过)')
    parser.add_argument('-dp', '--dump-sub-modules', action='store_true',
                        help='Enable sub-module dump / 启用子模块dump (默认只dump主要结果)')

    # 高级选项 / Advanced options
    parser.add_argument('--case', type=str, help='Specific test case to run / 要运行的特定测试用例')
    parser.add_argument('--output-dir', type=str, help='Output directory for results / 结果的输出目录')

    # Golden数据选项 / Golden data options
    parser.add_argument('--save-golden', type=str, nargs='?', const='auto',
                        help='Save current results as golden data / 将当前结果保存为golden数据')
    parser.add_argument('--list-golden', action='store_true',
                        help='List all available golden data versions / 列出所有可用的golden数据版本')
    parser.add_argument('--load-golden', type=str,
                        help='Load specific golden data version as current / 加载特定golden数据版本作为当前版本')

    return parser.parse_args()

def check_library_exists(lib_path: str, logger) -> bool:
    """
    Check if the library file exists and is valid.
    检查库文件是否存在且有效。

    Args / 参数:
        lib_path: Path to the library file / 库文件路径
        logger: Logger instance / 日志实例

    Returns / 返回:
        True if library exists and is valid / 如果库存在且有效则返回True
    """
    if not os.path.exists(lib_path):
        logger.error(f"❌ Library not found / 未找到库文件: {lib_path}")
        return False

    file_size = os.path.getsize(lib_path)
    if file_size == 0:
        logger.error(f"❌ Library file is empty / 库文件为空: {lib_path}")
        return False

    logger.info(f"✅ Library found / 找到库文件: {lib_path} ({file_size / (1024*1024):.1f} MB)")
    return True

def build_bokeh(builder, config, logger, is_build=False, clean=False):
    """
    Step 1: Build Bokeh SDK.
    步骤1：构建Bokeh SDK。

    Args / 参数:
        builder: BokehBuilder instance / BokehBuilder实例
        config: Configuration object / 配置对象
        logger: Logger instance / 日志实例
        is_build: Whether to build / 是否构建
        clean: Whether to clean build / 是否清理构建

    Returns / 返回:
        True if successful / 如果成功则返回True
    """
    if not is_build:
        logger.info("⏭️  Skipping build step (default) / 跳过构建步骤（默认）")
        # For SSH builds, assume library exists on remote server / 对于SSH构建，假设库文件在远程服务器上存在
        if config.get('device', 'ssh_host') and config.get('device', 'ssh_user'):
            logger.info("📋 SSH configured, assuming library exists on remote server / 已配置SSH，假设库文件在远程服务器上存在")
            return True
        else:
            # For local builds, check the build output / 对于本地构建，检查构建输出
            lib_path = builder.get_lib_path()
            if not check_library_exists(lib_path, logger):
                logger.error("❌ Library not found, please use --build to build first / 未找到库文件，请使用 --build 先构建")
                return False
            return True

    logger.info("🔨 Step 1: Building Bokeh SDK... / 步骤1：构建Bokeh SDK...")
    build_success = builder.build(clean=clean)

    if not build_success:
        logger.error("❌ Build failed / 构建失败")
        return False

    # For SSH builds, library is ready on remote server / 对于SSH构建，库文件已在远程服务器准备就绪
    # For local builds, check the build output / 对于本地构建，检查构建输出
    if config.get('device', 'ssh_host') and config.get('device', 'ssh_user'):
        logger.info("✅ SSH build completed, library ready on remote server / SSH构建完成，库文件已在远程服务器准备就绪")
    else:
        # For local builds, check the build output / 对于本地构建，检查构建输出
        lib_path = builder.get_lib_path()
        if not check_library_exists(lib_path, logger):
            logger.error("❌ Library check failed / 库检查失败")
            return False

    return True

def push_bokeh(pusher, logger, case=None):
    """
    Step 2: Push libraries to device.
    步骤2：推送库到设备。

    Args / 参数:
        pusher: DevicePusher instance / DevicePusher实例
        logger: Logger instance / 日志实例
        case: Specific test case / 特定测试用例

    Returns / 返回:
        True if successful / 如果成功则返回True
    """
    logger.info("📱 Step 2: Pushing to device... / 步骤2：推送到设备...")

    # Always use original push script (simplified) / 总是使用原始推送脚本（简化）
    logger.info("🔧 Using original 2-push script / 使用原始2-push脚本")
    push_success = pusher.push_libraries(use_original_script=True)

    if not push_success:
        logger.error("❌ Push failed / 推送失败")
        return False

    # Push test files if case is specified / 如果指定了测试用例则推送测试文件
    if case:
        pusher.push_test_files(case)

    return True

def run_offline_test(pusher, dump_manager, logger, dump_sub_modules=False, case=None):
    """
    Step 3: Run offline test and pull results.
    步骤3：运行离线测试并拉取结果。

    Args / 参数:
        pusher: DevicePusher instance / DevicePusher实例
        dump_manager: DumpManager instance / DumpManager实例
        logger: Logger instance / 日志实例
        dump_sub_modules: Whether to dump sub-modules / 是否dump子模块
        case: Specific test case / 特定测试用例

    Returns / 返回:
        True if successful / 如果成功则返回True
    """
    logger.info("🧪 Step 3: Running offline test... / 步骤3：运行离线测试...")

    # Configure dump settings / 配置dump设置
    logger.info("⚙️  Configuring dump settings... / 配置dump设置...")
    if dump_sub_modules:
        logger.info("📊 Enabling sub-module dump / 启用子模块dump")

        # Clear historical dump data before enabling sub-module dump / 启用子模块dump前清理历史数据
        logger.info("🧹 Clearing historical dump data... / 清理历史dump数据...")
        if not dump_manager.clear_dump_directory():
            logger.warning("⚠️  Failed to clear dump directory, continuing anyway / 清理dump目录失败，继续执行")

        # Enable all sub-module dumps / 启用所有子模块dump
        dump_manager.enable_module_dump('all')
    else:
        logger.info("📋 Using default dump (main results only) / 使用默认dump（仅主要结果）")
        dump_manager.enable_default_dumps()

    # Run the test / 运行测试
    logger.info("🧪 Running offline test... / 运行离线测试...")
    test_success = pusher.run_offline_test(case)

    if not test_success:
        logger.error("❌ Test execution failed / 测试执行失败")
        return False

    return True

def pull_results(pusher, output_dir, logger):
    """
    Pull results from device.
    从设备拉取结果。

    Args / 参数:
        pusher: DevicePusher instance / DevicePusher实例
        output_dir: Output directory / 输出目录
        logger: Logger instance / 日志实例

    Returns / 返回:
        Result path if successful / 如果成功则返回结果路径
    """
    logger.info("📥 Pulling results from device... / 从设备拉取结果...")
    result_path = pusher.pull_results(output_dir)
    return result_path

def main():
    """
    Main entry point for the Bokeh verification tool.
    Bokeh验证工具的主入口点。
    """
    # Parse arguments / 解析参数
    args = parse_arguments()

    # Handle golden data operations first / 首先处理golden数据操作
    if args.list_golden:
        # TODO: Implement list golden functionality / 实现列出golden功能
        print("Golden data versions will be listed here / Golden数据版本将在此列出")
        return 0

    # Setup logging / 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    setup_logging(log_level)
    logger = logging.getLogger('bokeh_verify')

    # Print banner / 打印横幅
    logger.info("🚀 Bokeh Verification Tool / Bokeh验证工具")
    logger.info("=" * 60)

    # Start timer / 启动计时器
    timer = Timer()
    timer.start()

    # Load configuration / 加载配置
    config = Config(args.config)

    # Override config with command line arguments / 用命令行参数覆盖配置
    if args.output_dir:
        config.set('output', 'dir', args.output_dir)

    # Create timestamp for this run / 为本次运行创建时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    run_id = f"bokeh_verify_{timestamp}"

    # Set up output directory / 设置输出目录
    output_dir = os.path.join(config.get('output', 'dir'), run_id)
    os.makedirs(output_dir, exist_ok=True)
    logger.info(f"📁 Output directory / 输出目录: {output_dir}")

    # Initialize components / 初始化组件
    builder = BokehBuilder(config)
    pusher = DevicePusher(config)
    dump_manager = DumpManager(config)
    comparator = ResultComparator(config)
    report_generator = ReportGenerator(config)

    try:
        # Simplified workflow: Build -> Push -> Test -> Pull / 简化的工作流程：构建 -> 推送 -> 测试 -> 拉取
        logger.info("🔄 Simplified workflow: Build -> Push -> Test -> Pull")
        logger.info("🔄 简化工作流程：构建 -> 推送 -> 测试 -> 拉取")

        # Step 1: Build (optional) / 步骤1：构建（可选）
        if not build_bokeh(builder, config, logger, is_build=args.build):
            return 1

        # Step 2: Push libraries / 步骤2：推送库
        if not push_bokeh(pusher, logger, args.case):
            return 1

        # Step 3: Run offline test / 步骤3：运行离线测试
        if not run_offline_test(pusher, dump_manager, logger, args.dump_sub_modules, args.case):
            return 1

        # Step 4: Pull results / 步骤4：拉取结果
        result_path = pull_results(pusher, output_dir, logger)

        # Success / 成功
        logger.info("✅ Verification completed successfully! / 验证成功完成！")
        logger.info(f"📁 Results saved to / 结果保存到: {output_dir}")

        # Handle golden data saving / 处理golden数据保存
        if args.save_golden:
            # TODO: Implement save golden functionality / 实现保存golden功能
            logger.info(f"📋 Golden data saving requested: {args.save_golden}")

        # Log completion time / 记录完成时间
        elapsed = timer.elapsed()
        logger.info(f"⏱️  Completed in {elapsed:.2f} seconds / 在 {elapsed:.2f} 秒内完成")

    except Exception as e:
        logger.error(f"💥 Error / 错误: {str(e)}", exc_info=True)
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
