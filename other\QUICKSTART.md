# Bokeh验证工具快速开始指南

## 前提条件

1. **Python环境**：确保安装了Python 3.8+
2. **Android设备**：连接并启用USB调试
3. **ADB工具**：确保adb命令可用
4. **NDK环境**：确保ndk-build命令可用

## 第一步：初始化项目

```bash
# 克隆或下载项目到本地
cd BokehVerifyToolsDev

# 初始化项目
python init_project.py --all

# 安装依赖
pip install -r requirements.txt
```

## 第二步：配置环境

编辑 `config.ini` 文件，设置基本配置：

```ini
[device]
series = X8U  # 你的设备系列

[build]
sdk_path = .  # Bokeh SDK的路径
```

## 第三步：运行验证工具

### 方法1：最简单的方式（推荐）

```bash
# 一键运行：自动检查前提条件、构建、推送
python run_bokeh_verify.py
```

### 方法2：使用主脚本自动模式

```bash
# 自动模式（默认X8U，自动构建和推送）
python bokeh_verify.py

# 查看详细输出
python bokeh_verify.py --verbose

# 清理构建
python bokeh_verify.py --clean
```

### 方法3：分步执行

```bash
# 只构建
python bokeh_verify.py --mode build

# 只推送
python bokeh_verify.py --mode push

# 构建并推送
python bokeh_verify.py --mode auto
```

### 方法4：运行测试脚本

```bash
# 运行完整的构建和推送测试
python test_build_push.py
```

## 第四步：验证结果

### 检查构建结果

构建成功后，应该能在以下位置找到库文件：
```
libs/arm64-v8a/libOPAlgoCamCaptureDualPortrait.so
```

### 检查推送结果

推送成功后，库文件应该在设备的以下位置：
```
/odm/lib64/libOPAlgoCamCaptureDualPortrait.so
```

可以使用以下命令验证：
```bash
adb shell ls -la /odm/lib64/libOPAlgoCamCaptureDualPortrait.so
```

## 常见问题

### 1. 构建失败

**问题**：`ndk-build: command not found`
**解决**：确保NDK已安装并添加到PATH环境变量

**问题**：找不到构建脚本
**解决**：确保在Bokeh SDK根目录下运行，且存在`1-build_dualcambokeh_sdk.py`

### 2. 推送失败

**问题**：`device unauthorized`
**解决**：在设备上允许USB调试授权

**问题**：`Read-only file system`
**解决**：确保设备已root并执行了`adb remount`

### 3. 设备检测失败

**问题**：找不到设备
**解决**：
```bash
# 检查设备连接
adb devices

# 重启adb服务
adb kill-server
adb start-server
```

## 下一步

构建和推送成功后，你可以：

1. **运行离线测试**：
   ```bash
   python bokeh_verify.py --mode verify
   ```

2. **添加测试用例**：
   在 `test_cases/` 目录下添加你的测试数据

3. **配置dump设置**：
   编辑配置文件中的dump相关设置

4. **生成验证报告**：
   运行完整的验证流程并查看生成的报告

## 支持

如果遇到问题，请：

1. 检查日志输出中的错误信息
2. 使用 `--verbose` 参数获取详细日志
3. 确认设备连接和权限设置
4. 检查配置文件是否正确
