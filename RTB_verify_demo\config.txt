[config]
PC_libpath = "W:/oppo_80398453/zx_code/hcs_rtb/22111_14.0.1.2_2024032104420000_root/rtb_algorithms/oppo_dualDepth_preview/DualDepthPreview/jni/ArcBokehSim/libs/"
SshBuildDebugCmd = "ssh 80398453@172.19.43.199 -t "cd ~/zx_code/hcs_rtb/22111_14.0.1.2_2024032104420000_root/rtb_algorithms/oppo_dualDepth_preview/DualDepthPreview/jni/ArcBokehSim; python ./build.py debug""
SshBuildSTSCmd = "ssh 80398453@172.19.43.199 -t "cd ~/zx_code/hcs_rtb/22111_14.0.1.2_2024032104420000_root/rtb_algorithms/oppo_dualDepth_preview/DualDepthPreview/jni/ArcBokehSim; python ./build.py sts_rel""
SshCleanLibCmd = ssh 80398453@172.19.43.199 -t "cd ~/zx_code/hcs_rtb/22111_14.0.1.2_2024032104420000_root/rtb_algorithms/oppo_dualDepth_preview/DualDepthPreview/jni/ArcBokehSim; rm -rf ./libs/;""
