1.背景
（1）当前bokeh代码离线测试case多、流程长，每次离线测试后需要手动对比结果，效率低。
（2）离线测试结果不包含中间dump数据，结果不一致时无法第一时间确定问题模块。需要手动设置dump开关重新跑2次（修改前后的代码，都要打开dump跑一次），重新dump 2次结果，通过Beyond Compare手动对比具体模块，跑完需要关闭dump开关，流程繁琐。
（3）当前代码提交流程自检结果不透明，难以判断某次提交是否影响其他模块。如果大家提交代码时，都附带verify自动对比且PASS的结果，会减少自检不充分的情况。
2.目标
（1）运行一次脚本，自动编译(可选)+推库(可选)+dump关键模块数据(可选具体模块)，自动化对比修改前后的结果，减少手动的对比。
（2）代码提交流程更规范，自检更充分，减少项目后期工作量。
3.使用流程
根据需求，打开对应模块dump开关；
运行一次离线测试程序并dump结果数据，作为对比脚本的参考数据--Data1；
代码修改后，重新运行一次离线测试程序，dump结果数据，作为对比数据--Data2；
运行对比脚本，自动对比Data1和Data2，如果容差值全部为0，则本次测试PASS，代表本次代码修改不会影响bokeh结果。如果某个模块Data1和Data2容差值不为0，会在脚本输出结果中标注出来，便于排查。

RTB verify样例：
图片: https://odocs.myoas.com/uploader/f/ouVBPNtlXrIle7bF.png?accessToken=eyJhbGciOiJIUzI1NiIsImtpZCI6ImRlZmF1bHQiLCJ0eXAiOiJKV1QifQ.eyJleHAiOjE3NDc5OTI5MTgsImZpbGVHVUlEIjoiRWUzMk0yV2pRWnNLajRBMiIsImlhdCI6MTc0Nzk5MjMxOCwiaXNzIjoidXBsb2FkZXJfYWNjZXNzX3Jlc291cmNlIiwidXNlcklkIjoyODIyODF9.hIPKnak3JtZFK_lHL1QLyWwe31Rj3QQYtmGmxBHSm7c

图片: https://odocs.myoas.com/uploader/f/n3i91RhXMjXMvpeJ.png?accessToken=eyJhbGciOiJIUzI1NiIsImtpZCI6ImRlZmF1bHQiLCJ0eXAiOiJKV1QifQ.eyJleHAiOjE3NDc5OTI5MTgsImZpbGVHVUlEIjoiRWUzMk0yV2pRWnNLajRBMiIsImlhdCI6MTc0Nzk5MjMxOCwiaXNzIjoidXBsb2FkZXJfYWNjZXNzX3Jlc291cmNlIiwidXNlcklkIjoyODIyODF9.hIPKnak3JtZFK_lHL1QLyWwe31Rj3QQYtmGmxBHSm7c
4.待办事项
1. 整理dump命令，检查depth、render等关键模块dump是否缺失，补全相关dump
比如离线测试只会dump depth final输出，如果自己负责rectify模块的修改，需要打开rectify现有开关，dump rectify数据。如果现有rectify dump不够细，无法满足需要，需要补充新的dump。
2. 准备合理的case数据（场景覆盖全面）
3. 编写自动化对比脚本，导通初版单case+固定场景demo

5.其他问题
1.当前代码存在的随机性问题，会对verify流程产生影响，需要评估：
1).是否存在算法原理层面无法消除的随机性，无法做到bit一致。
2).如果没有算法原理层面随机性，即流程导致问题，需优先解决随机性问题，再导通bit一致verify流程或者改用流程一致等方案


