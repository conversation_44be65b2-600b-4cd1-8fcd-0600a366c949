#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dump management module for Bokeh Verification Tool.

This module handles configuring and managing dump settings on the device.
"""

import os
import logging
from typing import List, Dict, Optional, Any

from config import Config
from utils import run_adb_command

class DumpManager:
    """Manager for Bokeh dump settings."""
    
    def __init__(self, config: Config):
        """
        Initialize the dump manager.
        
        Args:
            config: Configuration object.
        """
        self.logger = logging.getLogger('bokeh_verify.dump')
        self.config = config
        
        # Device configuration
        self.device_id = config.get('device', 'id')
        
        # Dump configuration
        self.dump_base_path = config.get('dump', 'dump_base_path')
        self.dump_local_path = config.get('dump', 'dump_local_path')
        
        # Dump levels
        self.depth_dump_level = config.get('dump', 'depth_dump_level')
        self.render_dump_level = config.get('dump', 'render_dump_level')
        self.rectify_dump_level = config.get('dump', 'rectify_dump_level')
        self.depth_warp_dump_level = config.get('dump', 'depth_warp_dump_level')
        self.create_new_dir = config.get('dump', 'create_new_dir')
        
        # Module mapping
        self.module_property_map = {
            'depth': 'persist.camera.bokeh.depth.dump.level',
            'render': 'persist.camera.bokeh.render.dump.level',
            'rectify': 'persist.camera.bokeh.dump.rectify',
            'depth_warp': 'persist.camera.bokeh.dump.depth.warp',
            'all': 'persist.camera.bokeh.dump.level',
        }
    
    def enable_module_dump(self, module: str, level: str = '3') -> bool:
        """
        Enable dump for a specific module.
        
        Args:
            module: Module name (depth, render, rectify, depth_warp, all).
            level: Dump level (0-3).
            
        Returns:
            True if successful, False otherwise.
        """
        if module not in self.module_property_map:
            self.logger.error(f"Unknown module: {module}")
            return False
        
        property_name = self.module_property_map[module]
        self.logger.info(f"Enabling dump for module {module} (level {level})")
        
        return_code, _, stderr = run_adb_command(
            f"shell setprop {property_name} {level}",
            device_id=self.device_id
        )
        
        if return_code != 0:
            self.logger.error(f"Failed to enable dump for module {module}: {stderr}")
            return False
        
        return True
    
    def disable_module_dump(self, module: str) -> bool:
        """
        Disable dump for a specific module.
        
        Args:
            module: Module name (depth, render, rectify, depth_warp, all).
            
        Returns:
            True if successful, False otherwise.
        """
        return self.enable_module_dump(module, '0')
    
    def enable_default_dumps(self) -> bool:
        """
        Enable default dumps based on configuration.
        
        Returns:
            True if all dumps were enabled successfully, False otherwise.
        """
        self.logger.info("Enabling default dumps...")
        
        # Create dump directory
        run_adb_command(
            f"shell mkdir -p {self.dump_base_path}",
            device_id=self.device_id
        )
        
        # Set dump levels
        success = True
        success &= self.enable_module_dump('depth', self.depth_dump_level)
        success &= self.enable_module_dump('render', self.render_dump_level)
        success &= self.enable_module_dump('rectify', self.rectify_dump_level)
        success &= self.enable_module_dump('depth_warp', self.depth_warp_dump_level)
        
        # Set create new directory option
        return_code, _, stderr = run_adb_command(
            f"shell setprop persist.camera.bokeh.dump.create.newdir {self.create_new_dir}",
            device_id=self.device_id
        )
        
        if return_code != 0:
            self.logger.error(f"Failed to set create new directory option: {stderr}")
            success = False
        
        return success
    
    def disable_all_dumps(self) -> bool:
        """
        Disable all dumps.
        
        Returns:
            True if all dumps were disabled successfully, False otherwise.
        """
        self.logger.info("Disabling all dumps...")
        
        success = True
        for module in self.module_property_map:
            success &= self.disable_module_dump(module)
        
        return success
    
    def get_dump_status(self) -> Dict[str, str]:
        """
        Get the current dump status.
        
        Returns:
            Dictionary mapping module names to their current dump levels.
        """
        self.logger.info("Getting dump status...")
        
        status = {}
        for module, property_name in self.module_property_map.items():
            _, stdout, _ = run_adb_command(
                f"shell getprop {property_name}",
                device_id=self.device_id
            )
            status[module] = stdout.strip() or '0'
        
        return status
    
    def enable_memory_trace(self, enable: bool = True) -> bool:
        """
        Enable or disable memory trace.
        
        Args:
            enable: Whether to enable memory trace.
            
        Returns:
            True if successful, False otherwise.
        """
        value = '1' if enable else '0'
        self.logger.info(f"{'Enabling' if enable else 'Disabling'} memory trace...")
        
        return_code, _, stderr = run_adb_command(
            f"shell setprop persist.camera.bokeh.enable.memory.trace {value}",
            device_id=self.device_id
        )
        
        if return_code != 0:
            self.logger.error(f"Failed to {'enable' if enable else 'disable'} memory trace: {stderr}")
            return False
        
        return True
    
    def enable_log_trace(self, enable: bool = True) -> bool:
        """
        Enable or disable log trace.
        
        Args:
            enable: Whether to enable log trace.
            
        Returns:
            True if successful, False otherwise.
        """
        value = '63' if enable else '0'
        self.logger.info(f"{'Enabling' if enable else 'Disabling'} log trace...")
        
        # Increase logcat buffer size
        if enable:
            run_adb_command(
                "logcat -G 64M",
                device_id=self.device_id
            )
        
        return_code, _, stderr = run_adb_command(
            f"shell setprop persist.camera.bokeh.log.status {value}",
            device_id=self.device_id
        )
        
        if return_code != 0:
            self.logger.error(f"Failed to {'enable' if enable else 'disable'} log trace: {stderr}")
            return False
        
        return True
    
    def clear_dump_directory(self) -> bool:
        """
        Clear the dump directory on the device.
        
        Returns:
            True if successful, False otherwise.
        """
        self.logger.info(f"Clearing dump directory: {self.dump_base_path}")
        
        return_code, _, stderr = run_adb_command(
            f"shell rm -rf {self.dump_base_path}/*",
            device_id=self.device_id
        )
        
        if return_code != 0:
            self.logger.error(f"Failed to clear dump directory: {stderr}")
            return False
        
        # Recreate the directory
        run_adb_command(
            f"shell mkdir -p {self.dump_base_path}",
            device_id=self.device_id
        )
        
        return True
