#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for CMD window functionality.
测试CMD窗口功能的脚本。
"""

import subprocess
import os

def test_cmd_window():
    """
    Test opening a separate CMD window.
    测试打开独立的CMD窗口。
    """
    print("测试CMD窗口功能 / Testing CMD window functionality...")
    print("将弹出独立的cmd窗口 / A separate cmd window will pop up...")
    
    try:
        # Create a simple test command / 创建一个简单的测试命令
        test_command = 'echo Hello from CMD window! && echo 来自CMD窗口的问候! && echo Press any key to close... && pause'
        
        # Use 'start' command to open a new cmd window / 使用'start'命令打开新的cmd窗口
        cmd_command = [
            'cmd', '/c', 'start', '/wait',
            'cmd', '/k',
            f'title Test CMD Window && {test_command} && exit'
        ]
        
        # Run the command / 运行命令
        result = subprocess.run(cmd_command,
                              cwd=os.getcwd(),
                              creationflags=subprocess.CREATE_NEW_CONSOLE if hasattr(subprocess, 'CREATE_NEW_CONSOLE') else 0)

        if result.returncode == 0:
            print("✅ CMD窗口测试成功 / CMD window test successful")
            return True
        else:
            print(f"❌ CMD窗口测试失败，退出代码: {result.returncode} / CMD window test failed with exit code: {result.returncode}")
            return False

    except Exception as e:
        print(f"❌ CMD窗口测试出错 / Error in CMD window test: {str(e)}")
        return False

if __name__ == "__main__":
    test_cmd_window()
