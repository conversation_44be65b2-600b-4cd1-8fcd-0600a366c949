# Bokeh 验证流程

本文档描述了Bokeh双目虚化算法的验证流程。

## 概述

验证流程包括以下步骤：

1. 构建Bokeh SDK（可选）
2. 将库推送到设备（可选）
3. 使用特定的dump设置运行离线测试
4. 比较修改后的代码与之前版本的结果
5. 生成突出显示任何差异的报告

## 详细流程

### 1. 构建Bokeh SDK

构建过程使用指定的构建脚本和参数编译Bokeh SDK。这可以在本地完成，也可以通过SSH连接到构建服务器完成。

```bash
python bokeh_verify.py --mode build
```

### 2. 推送库到设备

此步骤将编译好的库、模型和测试文件推送到设备。

```bash
python bokeh_verify.py --mode push
```

### 3. 运行离线测试

离线测试在测试用例上运行Bokeh算法并dump结果。

```bash
python bokeh_verify.py --mode verify
```

### 4. 比较结果

比较过程将dump的结果与参考数据进行比较，以识别任何差异。

### 5. 生成报告

生成报告以突出显示修改后的代码与参考数据之间的任何差异。

## 配置

验证流程使用`config.ini`文件进行配置。有关可用选项，请参阅模板文件。

## Dump模块

以下dump模块可用：

- **depth**：dump深度估计数据
- **render**：dump渲染数据
- **rectify**：dump校正数据
- **depth_warp**：dump深度变形数据

## 阈值

阈值用于确定修改后的代码与参考数据之间的差异是否可接受。阈值可以全局设置，也可以按模块设置。

## 输出

验证流程生成以下输出：

- **结果**：离线测试的原始输出
- **比较**：修改后的代码与参考数据之间的差异
- **报告**：汇总结果的HTML、文本或JSON报告

## 故障排除

如果验证流程失败，请检查以下内容：

1. 确保设备正确连接并可通过ADB访问
2. 检查库是否正确构建和推送
3. 验证dump设置是否正确
4. 检查日志中是否有错误消息
