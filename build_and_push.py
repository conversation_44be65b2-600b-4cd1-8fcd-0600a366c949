#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple build and push script for Bokeh SDK.
Bokeh SDK的简单构建和推送脚本。

This script provides a simplified interface for building and pushing Bokeh SDK.
该脚本为构建和推送Bokeh SDK提供简化的接口。
"""

import os
import sys
import argparse
import logging

# Add src directory to path / 将src目录添加到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from config import Config
from build import BokehBuilder
from push import DevicePusher
from utils import setup_logging

def parse_arguments():
    """
    Parse command line arguments.
    解析命令行参数。
    """
    parser = argparse.ArgumentParser(description='Bokeh SDK Build and Push Tool / Bokeh SDK构建和推送工具')
    
    # Operation mode / 操作模式
    parser.add_argument('--mode', type=str, choices=['build', 'push', 'both'],
                        default='both', help='Operation mode / 操作模式 (default: both)')
    
    # Device selection / 设备选择
    parser.add_argument('-s', '--series', type=str, help='Device series / 设备系列 (e.g., X8U)')
    parser.add_argument('-d', '--device', type=str, help='Device ID for ADB / 用于ADB的设备ID')
    
    # Build options / 构建选项
    parser.add_argument('-c', '--clean', action='store_true', help='Clean build / 清理构建')
    parser.add_argument('--build-type', type=str, choices=['normal', 'coverity', 'asan'],
                        default='normal', help='Build type / 构建类型 (default: normal)')
    
    # Configuration / 配置
    parser.add_argument('--config', type=str, default='config.simple.ini',
                        help='Configuration file / 配置文件 (default: config.simple.ini)')
    
    # Misc options / 其他选项
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output / 详细输出')
    
    return parser.parse_args()

def build_sdk(config: Config, clean: bool = False, build_type: str = 'normal') -> bool:
    """
    Build the Bokeh SDK.
    构建Bokeh SDK。
    
    Args / 参数:
        config: Configuration object / 配置对象
        clean: Whether to clean before building / 是否在构建前清理
        build_type: Build type / 构建类型
        
    Returns / 返回:
        True if successful / 如果成功则返回True
    """
    print(f"🔨 Building Bokeh SDK... / 构建Bokeh SDK...")
    print(f"   Series / 系列: {config.get('device', 'series')}")
    print(f"   Build Type / 构建类型: {build_type}")
    print(f"   Clean / 清理: {'Yes' if clean else 'No'}")
    
    builder = BokehBuilder(config)
    success = builder.build(clean=clean, build_type=build_type)
    
    if success:
        print("✅ Build completed successfully! / 构建成功完成！")
        lib_path = builder.get_lib_path()
        if os.path.exists(lib_path):
            file_size = os.path.getsize(lib_path) / (1024 * 1024)  # MB
            print(f"📦 Library: {lib_path} ({file_size:.1f} MB)")
        return True
    else:
        print("❌ Build failed! / 构建失败！")
        return False

def push_to_device(config: Config) -> bool:
    """
    Push libraries to device.
    将库推送到设备。
    
    Args / 参数:
        config: Configuration object / 配置对象
        
    Returns / 返回:
        True if successful / 如果成功则返回True
    """
    print(f"📱 Pushing to device... / 推送到设备...")
    
    pusher = DevicePusher(config)
    
    if not pusher.device_id:
        print("❌ No device found! / 未找到设备！")
        print("Please connect an Android device and enable USB debugging.")
        print("请连接Android设备并启用USB调试。")
        return False
    
    print(f"   Device / 设备: {pusher.device_id}")
    print(f"   Series / 系列: {pusher.series}")
    
    success = pusher.push_libraries()
    
    if success:
        print("✅ Push completed successfully! / 推送成功完成！")
        return True
    else:
        print("❌ Push failed! / 推送失败！")
        return False

def main():
    """
    Main entry point.
    主入口点。
    """
    args = parse_arguments()
    
    # Setup logging / 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    setup_logging(log_level)
    
    print("Bokeh SDK Build and Push Tool / Bokeh SDK构建和推送工具")
    print("=" * 60)
    
    # Check configuration file / 检查配置文件
    if not os.path.exists(args.config):
        print(f"❌ Configuration file not found: {args.config}")
        print(f"❌ 未找到配置文件: {args.config}")
        return 1
    
    # Load configuration / 加载配置
    config = Config(args.config)
    
    # Override config with command line arguments / 用命令行参数覆盖配置
    if args.series:
        config.set('device', 'series', args.series)
    if args.device:
        config.set('device', 'id', args.device)
    
    success = True
    
    # Execute based on mode / 根据模式执行
    if args.mode in ['build', 'both']:
        success &= build_sdk(config, clean=args.clean, build_type=args.build_type)
        
        if not success:
            print("❌ Build failed, skipping push.")
            print("❌ 构建失败，跳过推送。")
            return 1
    
    if args.mode in ['push', 'both']:
        success &= push_to_device(config)
    
    # Summary / 总结
    print("\n" + "=" * 60)
    if success:
        print("🎉 Operation completed successfully! / 操作成功完成！")
        
        if args.mode in ['both']:
            print("📋 Next steps / 后续步骤:")
            print("   1. Run verification: python bokeh_verify.py --mode verify")
            print("   1. 运行验证: python bokeh_verify.py --mode verify")
        
        return 0
    else:
        print("❌ Operation failed! / 操作失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
