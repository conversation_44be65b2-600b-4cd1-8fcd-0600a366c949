#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Bokeh build and push functionality.
Bokeh构建和推送功能的测试脚本。

This script tests the build and push functionality without running the full verification.
该脚本测试构建和推送功能，而不运行完整的验证。
"""

import os
import sys
import logging

# Add src directory to path / 将src目录添加到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from config import Config
from build import BokehBuilder
from push import DevicePusher
from utils import setup_logging

def test_build():
    """
    Test the build functionality.
    测试构建功能。
    """
    print("=" * 50)
    print("Testing Bokeh SDK Build / 测试Bokeh SDK构建")
    print("=" * 50)
    
    # Load configuration / 加载配置
    config = Config('config.simple.ini')
    
    # Initialize builder / 初始化构建器
    builder = BokehBuilder(config)
    
    # Test build / 测试构建
    print("Starting build... / 开始构建...")
    success = builder.build(clean=False)
    
    if success:
        print("✅ Build successful! / 构建成功！")
        
        # Check if library exists / 检查库是否存在
        lib_path = builder.get_lib_path()
        if os.path.exists(lib_path):
            print(f"✅ Library found: {lib_path}")
            print(f"✅ 找到库文件: {lib_path}")
        else:
            print(f"❌ Library not found: {lib_path}")
            print(f"❌ 未找到库文件: {lib_path}")
            return False
    else:
        print("❌ Build failed! / 构建失败！")
        return False
    
    return True

def test_push():
    """
    Test the push functionality.
    测试推送功能。
    """
    print("\n" + "=" * 50)
    print("Testing Library Push / 测试库推送")
    print("=" * 50)
    
    # Load configuration / 加载配置
    config = Config('config.simple.ini')
    
    # Initialize pusher / 初始化推送器
    pusher = DevicePusher(config)
    
    # Check if device is connected / 检查设备是否连接
    if not pusher.device_id:
        print("❌ No device connected! / 未连接设备！")
        print("Please connect an Android device and enable USB debugging.")
        print("请连接Android设备并启用USB调试。")
        return False
    
    print(f"📱 Device found: {pusher.device_id}")
    print(f"📱 找到设备: {pusher.device_id}")
    
    # Test push / 测试推送
    print("Starting push... / 开始推送...")
    success = pusher.push_libraries()
    
    if success:
        print("✅ Push successful! / 推送成功！")
    else:
        print("❌ Push failed! / 推送失败！")
        return False
    
    return True

def main():
    """
    Main test function.
    主测试函数。
    """
    # Setup logging / 设置日志
    setup_logging(logging.INFO)
    
    print("Bokeh Verification Tool - Build & Push Test")
    print("Bokeh验证工具 - 构建和推送测试")
    print("=" * 60)
    
    # Check if configuration file exists / 检查配置文件是否存在
    if not os.path.exists('config.simple.ini'):
        print("❌ Configuration file not found: config.simple.ini")
        print("❌ 未找到配置文件: config.simple.ini")
        print("Please run: python init_project.py --config")
        print("请运行: python init_project.py --config")
        return 1
    
    # Test build / 测试构建
    build_success = test_build()
    
    if not build_success:
        print("\n❌ Build test failed, skipping push test.")
        print("❌ 构建测试失败，跳过推送测试。")
        return 1
    
    # Test push / 测试推送
    push_success = test_push()
    
    # Summary / 总结
    print("\n" + "=" * 50)
    print("Test Summary / 测试总结")
    print("=" * 50)
    print(f"Build: {'✅ PASS' if build_success else '❌ FAIL'}")
    print(f"Push:  {'✅ PASS' if push_success else '❌ FAIL'}")
    print(f"构建: {'✅ 通过' if build_success else '❌ 失败'}")
    print(f"推送: {'✅ 通过' if push_success else '❌ 失败'}")
    
    if build_success and push_success:
        print("\n🎉 All tests passed! Ready for verification.")
        print("🎉 所有测试通过！准备进行验证。")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the logs.")
        print("❌ 部分测试失败。请检查日志。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
