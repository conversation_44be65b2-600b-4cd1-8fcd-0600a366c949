# Bokeh 验证工具

一个用于通过比较输出结果来验证Bokeh双目虚化算法修改的工具。

## 概述

该工具通过以下步骤自动化验证Bokeh算法修改的过程：

1. 编译Bokeh SDK（可选）
2. 将库推送到设备（可选）
3. 使用特定的dump设置运行离线测试
4. 比较修改后的代码与之前版本的结果
5. 生成突出显示任何差异的报告

## 功能特点

- 自动化的构建和推送过程
- 可配置的模块dump设置
- 输出图像和数据的详细比较
- 可视化差异报告
- 支持多个测试用例和场景
- 可自定义不同模块的容差阈值

## 前提条件

- Python 3.8+
- ADB (Android Debug Bridge)
- 具有适当权限的已连接Android设备
- Bokeh SDK源代码

## 安装

1. 克隆此仓库：
   ```
   git clone https://your-repository-url/bokeh-verify-tools.git
   cd bokeh-verify-tools
   ```

2. 安装所需依赖：
   ```
   pip install -r requirements.txt
   ```

3. 配置环境：
   ```
   python init_project.py --all
   # 编辑config.ini设置您的特定配置
   # 如果使用SSH远程构建，请复制并配置SSH配置文件
   cp config.ssh.ini.example config.ssh.ini
   # 编辑config.ssh.ini设置SSH连接信息
   ```

## 使用方法

### 最简单的使用方式（推荐）

```bash
# 一键运行：自动构建、检查、推送
python run_bokeh_verify.py
```

或者

```bash
# 使用主脚本的自动模式（默认X8U）
python bokeh_verify.py
```

### 主脚本详细使用

#### 自动模式（默认）

```bash
# 自动模式：构建 -> 检查 -> 推送
python bokeh_verify.py --mode auto

# 跳过构建步骤
python bokeh_verify.py --mode auto --no-build

# 跳过推送步骤
python bokeh_verify.py --mode auto --no-push

# 清理构建
python bokeh_verify.py --mode auto --clean
```

#### 单独步骤

```bash
# 只构建
python bokeh_verify.py --mode build

# 只推送
python bokeh_verify.py --mode push

# 只验证
python bokeh_verify.py --mode verify
```

#### 完整工作流

```bash
# 构建、推送和验证
python bokeh_verify.py --mode full
```

### 其他工具脚本

```bash
# 测试构建和推送功能
python test_build_push.py

# 使用独立的构建推送脚本
python build_and_push.py --mode both
```

## 配置

### 本地配置
编辑`config.simple.ini`以自定义：
- 设备系列（默认X8U）
- Dump模块设置
- 比较阈值
- 输出目录

### SSH远程构建配置
如果您的Bokeh项目在Linux服务器上，请使用SSH配置：

1. 复制SSH配置模板：
   ```bash
   cp config.ssh.ini.example config.ssh.ini
   ```

2. 编辑`config.ssh.ini`，配置SSH连接信息：
   ```ini
   [device]
   ssh_host = your.linux.server.com
   ssh_user = your_username
   ssh_password = your_password
   # 或使用SSH密钥
   # ssh_key_file = ~/.ssh/id_rsa

   [build]
   sdk_path = /path/to/your/bokeh/project
   ```

3. 测试SSH连接：
   ```bash
   python test_ssh_connection.py
   ```

4. 使用SSH配置运行：
   ```bash
   python bokeh_verify.py --config config.ssh.ini
   ```

## 项目结构

```
bokeh-verify-tools/
├── bokeh_verify.py         # 主脚本
├── config.ini              # 配置文件
├── requirements.txt        # Python依赖
├── src/
│   ├── build.py            # 构建工具
│   ├── push.py             # 设备推送工具
│   ├── dump.py             # Dump控制工具
│   ├── compare.py          # 比较逻辑
│   ├── report.py           # 报告生成
│   └── utils.py            # 通用工具
├── test_cases/             # 测试用例定义
└── docs/                   # 文档
```

## 验证流程

1. **构建阶段**：编译Bokeh SDK，生成所需的库文件
2. **推送阶段**：将库文件、模型和测试数据推送到设备
3. **测试阶段**：运行离线测试并dump结果
4. **比较阶段**：将dump结果与参考数据进行比较
5. **报告阶段**：生成详细的比较报告，突出显示任何差异

## 贡献

欢迎贡献！请随时提交Pull Request。

## 许可证

版权所有 (C) 2024 OPLUS Mobile Comm Corp., Ltd.
