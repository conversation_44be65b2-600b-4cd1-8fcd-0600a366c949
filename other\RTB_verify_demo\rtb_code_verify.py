import os
import time
import subprocess
import push_bokeh
import os
import shutil
import threading

def delete_files_in_folder(folder):
    if not os.path.exists(folder):
        return
    for filename in os.listdir(folder):
        file_path = os.path.join(folder, filename)
        try:
            if os.path.isfile(file_path) or os.path.islink(file_path):
                os.unlink(file_path)
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)
        except Exception as e:
            print('Failed to delete %s. Reason: %s' % (file_path, e))

def LOGI(*args):
    message = ' '.join(str(arg) for arg in args)
    print(f"{round(time.time() - start_time, 3)}: {message}")

def run_shell_cmd(adbcmd):
    cmd = "adb shell " + "\"" + adbcmd + "\""
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, shell=True)
    return result.stdout

# 定义要在新线程中执行的函数
def backRun(thread_function, time):
    #print(f"Thread {thread_function}: starting")
    thread = threading.Thread(target=thread_function, args=(time,))
    # 启动新线程
    thread.start()

def pushDataAndRunImageVerifyTest():
    LOGI("1. 准备验证数据 ... ")
    # 清理 output
    delete_files_in_folder("./outputs/")
    # 创建文件夹
    os.system("adb shell rm -rf ./data/local/tmp/PreviewBokeh/outputs/ ./sdcard/Android/data/com.oplus.camera/vbdump/")
    os.system("adb shell mkdir ./sdcard/Android/data/com.oplus.camera/vbdump/ ./data/local/tmp/PreviewBokeh/inputs/ \
    ./data/local/tmp/PreviewBokeh/outputs/ ./data/local/tmp/PreviewBokeh/inputs/1111720263011/") 

    LOGI("1.1 开始push资料包 rtb ... ")
    #os.system("adb push input_list.json ./data/local/tmp/PreviewBokeh/")
    os.system("adb push input_list.txt ./data/local/tmp/PreviewBokeh/")
    #os.system("adb push --sync ./verify_data/1111720263011.zip ./data/local/tmp/PreviewBokeh/inputs/")
    #os.system("adb shell unzip -q  ./data/local/tmp/PreviewBokeh/inputs/1111720263011.zip -o -d ./data/local/tmp/PreviewBokeh/inputs/")
    os.system("adb push --sync ./verify_data/1111720263011 /data/local/tmp/PreviewBokeh/inputs/")

    #开始运行程序
    LOGI("2. 开始运行 rtb ... ")
    os.system('adb shell \"export LD_LIBRARY_PATH=/odm/lib64 && export ADSP_LIBRARY_PATH=/odm/lib/rfsa/adsp && /data/local/tmp/PreviewBokeh/testpreview /data/local/tmp/PreviewBokeh/input_list.txt\"')

    LOGI("rtb 运行结束,pull 结果")

    os.system("rm -rf ./outputs/")
    os.system("adb pull ./data/local/tmp/PreviewBokeh/outputs/ ./outputs/")
    os.system("adb pull ./sdcard/Android/data/com.oplus.camera/vbdump ./outputs/")

# dump出结果进行对比
from PIL import Image, ImageChops, ImageStat
def compare_images(img1_path, img2_path):
    img1 = Image.open(img1_path)
    img2 = Image.open(img2_path)

    diff = ImageChops.difference(img1, img2)

    # Calculate the root-mean-square difference 方差
    stat = ImageStat.Stat(diff)
    #rms = sum([value ** 2 for value in stat.mean]) ** 0.5
    diff_avg =float(sum(stat.mean)) / len(stat.mean)
    #if diff_avg > 10:
        # The following line can be used to visualize the difference image
        #diff.show()

    return diff_avg


COMMON_PIXEL_ERROR = 10
dump_name_list = [
["_input_yuv_texture_1280x960", COMMON_PIXEL_ERROR],
["_wxh=240x320_srcGrayMain", COMMON_PIXEL_ERROR],
["_wxh=240x320_srcGraySalve", COMMON_PIXEL_ERROR],
["_wxh=240x320_srcYuvMain", COMMON_PIXEL_ERROR],
#depth
["_wxh=240x320_turbo_mainRect", COMMON_PIXEL_ERROR],
["_wxh=240x320_turbo_slaveRect", COMMON_PIXEL_ERROR],
["_wxh=256x320_turbo_ai_depth", COMMON_PIXEL_ERROR],
["_wxh=320x240_turbo_ai_depth_unrect", COMMON_PIXEL_ERROR],
#ai refine
["_wxh=320x240_refine_rgb", COMMON_PIXEL_ERROR], #rgbPtr
["_wxh=320x240_rough", COMMON_PIXEL_ERROR],   #refine mBaseDepthImage
["_wxh=320x240_refine", COMMON_PIXEL_ERROR],  #output
#rendering
#["_input_render_param_in.txt", COMMON_PIXEL_ERROR],
["_inputY_1280x960", COMMON_PIXEL_ERROR],
["_inputUV_640x480_u", COMMON_PIXEL_ERROR],
["_inputUV_640x480_v", COMMON_PIXEL_ERROR],
["_depth_320x240", COMMON_PIXEL_ERROR],
["_inputY_preproc_640x480", COMMON_PIXEL_ERROR],
["_depth_postproc_320x240", COMMON_PIXEL_ERROR],  #preprocess
["_blurmap_normalize_320x240", COMMON_PIXEL_ERROR], #getBlurMap
["_edgemap_320x240", COMMON_PIXEL_ERROR], #getEdgeErrorMask
["_gatherY_640x480", COMMON_PIXEL_ERROR], #getBlurImg
["_gatherY_final_640x480", COMMON_PIXEL_ERROR], #postprocessBlurImg

["_alphamap_origin_320x240", COMMON_PIXEL_ERROR], #getAlphaMap
["_alphamap_dilate5_320x240", COMMON_PIXEL_ERROR], #getAlphaMap
["_alphamap_dilate5_gauss5_320x240", COMMON_PIXEL_ERROR], #getAlphaMap
["_alphamap_final_1280x960", COMMON_PIXEL_ERROR], #getAlphaMap
["_bokehOutImg_y_1280x960", COMMON_PIXEL_ERROR],  #alphaBlend
["_bokehOutImg_640x480_u", COMMON_PIXEL_ERROR],   #alphaBlend
["_bokehOutImg_640x480_v", COMMON_PIXEL_ERROR],   #alphaBlend
["_final_result", COMMON_PIXEL_ERROR],
]

refimg2_path = './verify_data/1111720263011/' #参考数据
dumpedimg1_path = './outputs/vbdump/' #运行结果
time.time()
def verify_compare_same_name_files_in_directories(dumpedimg1_path, refimg2_path):
    print("dumpedimg path:", dumpedimg1_path)
    print("refimg path:", refimg2_path)
    
    #确认运行成功
    out_cnt = 0
    for file_name in os.listdir(dumpedimg1_path):
        if(file_name.count("final_result.ppm")) > 0:
            out_cnt = out_cnt + 1
    if(out_cnt == 0):
        LOGI("确认程序运行结果完整性...")
        print("\nverify_result:", "\n#### verify FAIL！！！####\n", "run fail please check the log.")
        return
    else:
        LOGI("程序运行完成")

    #比较差异
    LOGI("4. 确认运行结果和golden数据比较差异...")
    max_error = 0
    #for file_name in os.listdir(dumpedimg1_path):
    for frameId in range(10):    # 1-4
        print("\nframe:", frameId+1, "\n")
        for i in range(len(dump_name_list)):
            #print("file_name", dump_name_list[i])
            file_name = ''+str(frameId + 1)+dump_name_list[i][0]+'.ppm'
            dumpedimg1_full_path = os.path.join(dumpedimg1_path, file_name)
            refimg2_full_path = os.path.join(refimg2_path, file_name)
            #print("dumpedimg path:", dumpedimg1_full_path, refimg2_full_path)
            if os.path.isfile(refimg2_full_path):
                if (os.path.isfile(dumpedimg1_full_path)):
                    #print(dir2_path, ":", file_name)
                    #print(f'Comparing {file_name} in {dir1_path} and {dir2_path}:')
                    pixel_avg_diff = int(compare_images(dumpedimg1_full_path, refimg2_full_path));
                    #if (pixel_avg_diff != 0):
                    print(dumpedimg1_full_path, "pixel average diff error:\t", (pixel_avg_diff))
                    if (pixel_avg_diff > max_error):
                        max_error = pixel_avg_diff
                else:
                    print(file_name, ":file not exits in dump")
            else:
                print(file_name, ":no golden data skip.")
    # 验证 max_error最大误差
    if max_error > 5:
        print("\n\033[31mverify_result:", "\n#### Case_ImageVerifyTest FAIL！！！####\n", "Please check the pixel average diff error.\033[0m")
    else:
        print("\n\033[32mverify_result:", "\n#### Case_ImageVerifyTest PASS！ ####\033[0m\n")
    print("\n")

def runstableTest(time_s):
    run_shell_cmd("export LD_LIBRARY_PATH=/odm/lib64 && export ADSP_LIBRARY_PATH=/odm/lib/rfsa/adsp && LD_HWASAN=1 /data/local/tmp/PreviewBokeh/testpreview /data/local/tmp/PreviewBokeh/input_list.txt CaseStableTest " + str(time_s * 2)) # why * time_s, 多写一些时间，测试程序运行实际时间比输入参数时间短。

def Case_ImageVerifyTest(isBuildNew, isPushNew):
    ###### Verify 图形质量 #######
    # 编译debug版本库，并push
    if isBuildNew:
        push_bokeh.buildDebug()
    if isPushNew:
        print("\n开始push so: \n\t如果模型文件有更新，需要手动运行push\n")
        push_bokeh.pushRtbLib()
    # 是否开关HCS
    os.system("adb shell setprop persist.camera.bokeh.preview.hcs_on 1")
    # 开启dump
    push_bokeh.openDump()
    push_bokeh.openLogTrace()
    # 准备数据运行脚本
    pushDataAndRunImageVerifyTest()

    # 准备数据运行脚本
    verify_compare_same_name_files_in_directories(dumpedimg1_path, refimg2_path)
    print("运行时间:", int(time.time() - start_time), "s")
    # 关闭dump -- 
    push_bokeh.closeDump()

# Case_StableTest原理：启动testpreview进程，进程中会同时高帧率, 运行预览40fps和拍照20fps的处理。观察是否有内存非法访问等问题
def Case_StableTest(isBuildNew, isPushNew):
    ###### 稳定性验证 #######
    ConfigThreadCnt = 4     # >= 2
    ConfigRunTime = 60 * 5  #单位s
    print("\n\n准备稳定性测试：\n全程约7min \n开始编译sts_rel版本库, 并push so, 预计2min")
    time.sleep(2)
    if isBuildNew:
        push_bokeh.buildSts()
    if isPushNew:
        push_bokeh.pushRtbLib()
    if isBuildNew:
        push_bokeh.cleanRemoteLib()
    LOGI("\n\n开始sts测试， 测试时间6-8min")
    os.system("adb shell date")
    testfailCnt = 10
    testfail = False

    # "开启测试线程组数(ODNN 最大10组？)："
    print("启动测试进程数量：", ConfigThreadCnt)
    # 开始多线程运行
    for i in range(ConfigThreadCnt):
        backRun(runstableTest, ConfigRunTime)
        #time.sleep(2)
    # 检查运行状态
    time.sleep(1) # 等线程启动完成
    start_time = time.time()
    for i in range(int(ConfigRunTime) +1): # 稳定性测试5min
        #print(run_shell_cmd("ps -A|grep -i testpreview"))
        executing_cnt = run_shell_cmd("ps -A|grep -i testpreview|wc -l")
        running_cnt = (int("0" +executing_cnt));
        pass_time = time.time() - start_time
        print("运行时间:", int(pass_time), "s", "剩余时间：",int(ConfigRunTime-pass_time),"s"," Process in running:", running_cnt, "(expect:", ConfigThreadCnt,")", end='\r')
        if pass_time > ConfigRunTime:
            break #时间结束
        if running_cnt < ConfigThreadCnt:
            testfailCnt = testfailCnt + 1
        if testfailCnt > 20: # 多次确认
            testfail = True
            break
        time.sleep(1)
    if testfail:
        print("\n\n\033[31mCase_StableTest运行结束: FAIL\033[0m\n")
    else:
        print("\n\n\033[32mCase_StableTest运行结束: PASS\033[0m\n")
    # 强制清理所有的testpreview
    for i in range(ConfigThreadCnt + 1):
        run_shell_cmd("kill `pidof -s testpreview`")
    # 强制清理所有的testpreview
    print("运行时间:", int(time.time() - start_time), "s")

def RecoveryDebugLib():
    # 编译debug版本库，并push
    push_bokeh.buildDebug()
    push_bokeh.pushRtbLib()
    print("#### 版本验证结束 ####")
########  main start ########
start_time = time.time()
#读取config.txt中编译路径等 配置信息
os.system("adb shell am start -n com.oplus.camera/com.oplus.camera.Camera -a com.oplus.camera.action.SHORTCUT_TYPE_MENU --ei mode 2") #目的：让app创建dump文件夹
push_bokeh.readConfig()
if __name__ == "__main__":
    Case_ImageVerifyTest(isBuildNew=True, isPushNew=True)
    Case_StableTest(isBuildNew=True, isPushNew=True)
    #RecoveryDebugLib() # Case_StableTest push  STS库开启了ascan，无法打开cameraapp。所以需要再push回debug

# setprop persist.camera.bokeh.preview.hcs_on 1
