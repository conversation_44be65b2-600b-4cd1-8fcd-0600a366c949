#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build module for Bokeh Verification Tool / Bokeh验证工具构建模块

This module handles building the Bokeh SDK.
该模块处理Bokeh SDK的构建。
"""

import os
import logging
import subprocess
import sys
from typing import Optional, Tuple

try:
    import paramiko
    PARAMIKO_AVAILABLE = True
except ImportError:
    PARAMIKO_AVAILABLE = False
    print("Warning: paramiko not available. SSH functionality will be disabled.")

from config import Config
from utils import run_command, Timer
import common

class BokehBuilder:
    """
    Builder for Bokeh SDK / Bokeh SDK构建器
    """

    def __init__(self, config: Config):
        """
        Initialize the builder.
        初始化构建器。

        Args / 参数:
            config: Configuration object / 配置对象
        """
        self.logger = logging.getLogger('bokeh_verify.build')
        self.config = config

        # Build configuration / 构建配置
        self.sdk_path = config.get('build', 'sdk_path', '.')
        self.build_script = config.get('build', 'build_script', '1-build_dualcambokeh_sdk.py')
        self.series = config.get('device', 'series', 'X8U')
        self.lib_type = config.getint('build', 'lib_type', 0)  # 0=normal, 1=coverity, 2=asan
        self.clean_type = config.getint('build', 'clean_type', 0)  # 0=false, 1=true

        # SSH configuration (for future use) / SSH配置（供将来使用）
        self.ssh_host = config.get('device', 'ssh_host', '')
        self.ssh_user = config.get('device', 'ssh_user', '')
        self.ssh_port = config.getint('device', 'ssh_port', 22)

    def build(self, clean: bool = False, build_type: str = None) -> bool:
        """
        Build the Bokeh SDK.
        构建Bokeh SDK。

        Args / 参数:
            clean: Whether to clean before building / 是否在构建前清理
            build_type: Build type (normal, coverity, asan) / 构建类型（normal, coverity, asan）

        Returns / 返回:
            True if build succeeded, False otherwise / 如果构建成功则返回True，否则返回False
        """
        timer = Timer()
        timer.start()

        # Determine lib_type based on build_type / 根据build_type确定lib_type
        lib_type = self.lib_type
        if build_type == "coverity":
            lib_type = 1
        elif build_type == "asan":
            lib_type = 2
        elif build_type == "normal":
            lib_type = 0

        # Set clean_type if clean is requested / 如果请求清理则设置clean_type
        clean_type = 1 if clean else self.clean_type

        self.logger.info(f"Building Bokeh SDK / 构建Bokeh SDK - Series: {self.series}, LibType: {lib_type}, Clean: {clean_type}")

        # Check if we need to build locally or via SSH / 检查是否需要本地构建或通过SSH构建
        if self.ssh_host and self.ssh_user:
            success = self._build_via_ssh(lib_type, clean_type)
        else:
            success = self._build_local(lib_type, clean_type)

        elapsed = timer.stop()
        if success:
            self.logger.info(f"Build completed successfully in {elapsed:.2f} seconds / 构建在{elapsed:.2f}秒内成功完成")
        else:
            self.logger.error(f"Build failed after {elapsed:.2f} seconds / 构建在{elapsed:.2f}秒后失败")

        return success

    def _build_local(self, lib_type: int, clean_type: int) -> bool:
        """
        Build the SDK locally.
        本地构建SDK。

        Args / 参数:
            lib_type: Library type (0=normal, 1=coverity, 2=asan) / 库类型
            clean_type: Clean type (0=false, 1=true) / 清理类型

        Returns / 返回:
            True if build succeeded, False otherwise / 如果构建成功则返回True，否则返回False
        """
        # Construct build command / 构造构建命令
        build_cmd = f"python {self.build_script} -s {self.series} -l {lib_type} -c {clean_type}"

        self.logger.info(f"Executing build command / 执行构建命令: {build_cmd}")

        try:
            return_code, stdout, stderr = run_command(
                build_cmd,
                shell=True,
                cwd=self.sdk_path
            )

            if return_code != 0:
                self.logger.error(f"Build failed with return code {return_code} / 构建失败，返回码: {return_code}")
                self.logger.error(f"stderr: {stderr}")
                return False

            self.logger.debug(f"Build output / 构建输出: {stdout}")

            # Check if the library was built successfully / 检查库是否构建成功
            lib_path = os.path.join(self.sdk_path, "libs", common.TARGET_ARCH_ABI, common.LIB_NAME)
            if os.path.exists(lib_path):
                self.logger.info(f"Library built successfully / 库构建成功: {lib_path}")
                return True
            else:
                self.logger.error(f"Library not found after build / 构建后未找到库: {lib_path}")
                return False

        except Exception as e:
            self.logger.error(f"Error during build / 构建过程中出错: {str(e)}")
            return False

    def _build_via_ssh(self, lib_type: int, clean_type: int) -> bool:
        """
        Build the SDK via SSH.
        通过SSH构建SDK。

        Args / 参数:
            lib_type: Library type / 库类型
            clean_type: Clean type / 清理类型

        Returns / 返回:
            True if build succeeded, False otherwise / 如果构建成功则返回True，否则返回False
        """
        if not PARAMIKO_AVAILABLE:
            self.logger.error("paramiko not available, cannot use SSH build / paramiko不可用，无法使用SSH构建")
            return False

        self.logger.info(f"Building via SSH on {self.ssh_host} / 通过SSH在{self.ssh_host}上构建")

        try:
            # Connect to SSH server / 连接到SSH服务器
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Get SSH password from config or prompt / 从配置获取SSH密码或提示输入
            ssh_password = self.config.get('device', 'ssh_password', '')
            ssh_key_file = self.config.get('device', 'ssh_key_file', '')

            if ssh_key_file and os.path.exists(ssh_key_file):
                # Use SSH key authentication / 使用SSH密钥认证
                self.logger.info("Using SSH key authentication / 使用SSH密钥认证")
                ssh.connect(
                    self.ssh_host,
                    port=self.ssh_port,
                    username=self.ssh_user,
                    key_filename=ssh_key_file
                )
            elif ssh_password:
                # Use password authentication / 使用密码认证
                self.logger.info("Using password authentication / 使用密码认证")
                ssh.connect(
                    self.ssh_host,
                    port=self.ssh_port,
                    username=self.ssh_user,
                    password=ssh_password
                )
            else:
                # Try without password (for key-based auth) / 尝试无密码连接（用于基于密钥的认证）
                self.logger.info("Trying passwordless authentication / 尝试无密码认证")
                ssh.connect(
                    self.ssh_host,
                    port=self.ssh_port,
                    username=self.ssh_user
                )

            # Construct and execute the build command / 构造并执行构建命令
            build_cmd = f"cd {self.sdk_path} && python {self.build_script} -s {self.series} -l {lib_type} -c {clean_type}"
            self.logger.info(f"SSH command / SSH命令: {build_cmd}")

            stdin, stdout, stderr = ssh.exec_command(build_cmd)
            exit_status = stdout.channel.recv_exit_status()

            # Read output / 读取输出
            stdout_text = stdout.read().decode('utf-8')
            stderr_text = stderr.read().decode('utf-8')

            if exit_status != 0:
                self.logger.error(f"SSH build failed with exit status {exit_status} / SSH构建失败，退出状态: {exit_status}")
                self.logger.error(f"stderr: {stderr_text}")
                ssh.close()
                return False

            self.logger.debug(f"SSH build output / SSH构建输出: {stdout_text}")

            # Check if the library was built successfully / 检查库是否构建成功
            lib_check_cmd = f"ls -la {self.sdk_path}/libs/{common.TARGET_ARCH_ABI}/{common.LIB_NAME}"
            stdin, stdout, stderr = ssh.exec_command(lib_check_cmd)
            exit_status = stdout.channel.recv_exit_status()

            if exit_status == 0:
                lib_info = stdout.read().decode('utf-8').strip()
                self.logger.info(f"Library built successfully / 库构建成功: {lib_info}")
                self.logger.info("✅ SSH build completed, library ready on remote server / SSH构建完成，库文件已在远程服务器准备就绪")

                ssh.close()
                return True
            else:
                self.logger.error(f"Library not found after build / 构建后未找到库")
                ssh.close()
                return False

        except Exception as e:
            self.logger.error(f"Error during SSH build / SSH构建过程中出错: {str(e)}")
            return False

    def _download_library_via_ssh(self, ssh) -> bool:
        """
        Download library file via SSH.
        通过SSH下载库文件。

        Args / 参数:
            ssh: SSH client / SSH客户端

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        try:
            # Create SFTP client / 创建SFTP客户端
            sftp = ssh.open_sftp()

            # Create local directory / 创建本地目录
            local_lib_dir = os.path.join("libs", common.TARGET_ARCH_ABI)
            os.makedirs(local_lib_dir, exist_ok=True)

            # Expand ~ to full path for SFTP
            if self.sdk_path.startswith('~'):
                # Get home directory from SSH
                stdin, stdout, stderr = ssh.exec_command('echo $HOME')
                home_dir = stdout.read().decode('utf-8').strip()
                remote_sdk_path = self.sdk_path.replace('~', home_dir)
            else:
                remote_sdk_path = self.sdk_path

            # Download library file / 下载库文件
            remote_lib_path = f"{remote_sdk_path}/libs/{common.TARGET_ARCH_ABI}/{common.LIB_NAME}"
            local_lib_path = os.path.join(local_lib_dir, common.LIB_NAME)

            self.logger.info(f"Downloading {remote_lib_path} -> {local_lib_path}")
            sftp.get(remote_lib_path, local_lib_path)

            # Verify download / 验证下载
            if os.path.exists(local_lib_path):
                file_size = os.path.getsize(local_lib_path) / (1024 * 1024)  # MB
                self.logger.info(f"✅ Download successful: {local_lib_path} ({file_size:.1f} MB)")

                sftp.close()
                return True
            else:
                self.logger.error(f"❌ Download failed: {local_lib_path}")
                sftp.close()
                return False

        except Exception as e:
            self.logger.error(f"Error downloading library / 下载库文件时出错: {str(e)}")
            return False

    def get_build_output_path(self) -> str:
        """
        Get the path to the build output.
        获取构建输出的路径。

        Returns / 返回:
            Path to the build output / 构建输出的路径
        """
        return os.path.join(self.sdk_path, "libs", common.TARGET_ARCH_ABI)

    def get_lib_path(self) -> str:
        """
        Get the path to the built library.
        获取构建库的路径。

        Returns / 返回:
            Path to the built library / 构建库的路径
        """
        return os.path.join(self.get_build_output_path(), common.LIB_NAME)

    def download_build_artifacts(self, local_dir: str = "libs") -> bool:
        """
        Download build artifacts from remote server via SSH.
        通过SSH从远程服务器下载构建产物。

        Args / 参数:
            local_dir: Local directory to save artifacts / 保存产物的本地目录

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        if not self.ssh_host or not self.ssh_user:
            self.logger.info("No SSH configuration, skipping download / 无SSH配置，跳过下载")
            return True

        if not PARAMIKO_AVAILABLE:
            self.logger.error("paramiko not available, cannot download artifacts / paramiko不可用，无法下载产物")
            return False

        self.logger.info(f"Downloading build artifacts from {self.ssh_host} / 从{self.ssh_host}下载构建产物")

        try:
            # Connect to SSH server / 连接到SSH服务器
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Get SSH credentials / 获取SSH凭据
            ssh_password = self.config.get('device', 'ssh_password', '')
            ssh_key_file = self.config.get('device', 'ssh_key_file', '')

            if ssh_key_file and os.path.exists(ssh_key_file):
                ssh.connect(self.ssh_host, port=self.ssh_port, username=self.ssh_user, key_filename=ssh_key_file)
            elif ssh_password:
                ssh.connect(self.ssh_host, port=self.ssh_port, username=self.ssh_user, password=ssh_password)
            else:
                ssh.connect(self.ssh_host, port=self.ssh_port, username=self.ssh_user)

            # Create SFTP client / 创建SFTP客户端
            sftp = ssh.open_sftp()

            # Create local directory / 创建本地目录
            local_lib_dir = os.path.join(local_dir, common.TARGET_ARCH_ABI)
            os.makedirs(local_lib_dir, exist_ok=True)

            # Download library file / 下载库文件
            # Expand ~ to full path for SFTP
            if self.sdk_path.startswith('~'):
                # Get home directory from SSH
                stdin, stdout, stderr = ssh.exec_command('echo $HOME')
                home_dir = stdout.read().decode('utf-8').strip()
                remote_sdk_path = self.sdk_path.replace('~', home_dir)
            else:
                remote_sdk_path = self.sdk_path

            remote_lib_path = f"{remote_sdk_path}/libs/{common.TARGET_ARCH_ABI}/{common.LIB_NAME}"
            local_lib_path = os.path.join(local_lib_dir, common.LIB_NAME)

            self.logger.info(f"Downloading {remote_lib_path} -> {local_lib_path}")
            sftp.get(remote_lib_path, local_lib_path)

            # Verify download / 验证下载
            if os.path.exists(local_lib_path):
                file_size = os.path.getsize(local_lib_path) / (1024 * 1024)  # MB
                self.logger.info(f"✅ Download successful: {local_lib_path} ({file_size:.1f} MB)")

                sftp.close()
                ssh.close()
                return True
            else:
                self.logger.error(f"❌ Download failed: {local_lib_path}")
                sftp.close()
                ssh.close()
                return False

        except Exception as e:
            self.logger.error(f"Error downloading artifacts / 下载产物时出错: {str(e)}")
            return False
