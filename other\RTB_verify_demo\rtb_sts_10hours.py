import os
import subprocess
import threading
import time
import push_bokeh


def run_shell_cmd(adbcmd):
    cmd = "adb shell " + "\"" + adbcmd + "\""
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, shell=True)
    return result.stdout
# 定义要在新线程中执行的函数
def backRun_NoArg(thread_function):
    print(f"Thread {thread_function}: starting")
    thread = threading.Thread(target=thread_function)
    # 启动新线程
    thread.start()
    print(f"Thread {thread_function}: finishing")

def aging():
    run_shell_cmd("export LD_LIBRARY_PATH=/odm/lib64 && export ADSP_LIBRARY_PATH=/odm/lib/rfsa/adsp && LD_HWASAN=1 /data/local/tmp/PreviewBokeh/testpreview /data/local/tmp/PreviewBokeh/input_list.txt CaseStableTest 36000")

##### main ####
threadCnt = input("开启测试线程组数(ODNN 最大10组？)：")
for i in range(int(threadCnt)):
    print("启动线程：", i)
    backRun_NoArg(aging)

for i in range(10*3600):
    executing_cnt = run_shell_cmd("ps -A|grep -i testpreview|wc -l")
    print("运行时间:", i * 5 , "s", "实例剩余：", int("0" +executing_cnt), "/", threadCnt)
    time.sleep(5)

