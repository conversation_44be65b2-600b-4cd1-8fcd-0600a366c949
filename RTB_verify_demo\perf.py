import time
import os
import sys
print(sys.argv)

def openMemDebug():
    os.system("adb shell setprop vendor.camera.mem.debug.enable 1")
    os.system("adb shell setprop persist.camera.bokeh.preview.enable.memory.trace 1")
    os.system("adb shell setprop vendor.camera.mem.debug.enable 1")

def openDump():
    dumpennable = '1'
    dumprender = '3'
    os.system("adb shell mkdir ./sdcard/Android/data/com.oplus.camera/vbdump")
    os.system("adb shell rm -rf ./sdcard/Android/data/com.oplus.camera/vbdump/*")
    os.system("adb shell setprop persist.camera.bokeh.preview.render.dump.level " + dumprender )
    os.system("adb shell setprop persist.camera.bokeh.preview.depth.dump.level " + dumpennable)
    os.system("adb shell setprop persist.camera.bokeh.preview.dump.rectify " + dumpennable)
    os.system("adb shell setprop persist.camera.bokeh.preview.dump.depth.warp " + dumpennable)

    #os.system('adb shell \"export LD_LIBRARY_PATH=/odm/lib64 && export ADSP_LIBRARY_PATH=/odm/lib/rfsa/adsp && /data/local/tmp/PreviewBokeh/testpreview /data/local/tmp/PreviewBokeh/input_list.txt\"')

def closeDump():
    dumpennable = '0'
    dumprender = '0'
    os.system("adb shell mkdir ./sdcard/Android/data/com.oplus.camera/vbdump/")
    os.system("adb shell setprop persist.camera.bokeh.preview.render.dump.level " + dumprender )
    os.system("adb shell setprop persist.camera.bokeh.preview.depth.dump.level " + dumpennable)
    os.system("adb shell setprop persist.camera.bokeh.preview.dump.rectify " + dumpennable)
    os.system("adb shell setprop persist.camera.bokeh.preview.dump.depth.warp " + dumpennable)

def traceOn():
    os.system("adb shell setprop persist.camera.bokeh.preview.enable.perf.trace 1")
    os.system("adb shell setprop rw.hcs.atrace.enable 1")

def logOn():
    print("开启log")
    os.system("adb shell setprop persist.camera.bokeh.preview.log.status 63")
    os.system("adb shell setprop rw.hcs.log.level 1") # 开启hcs log
    os.system("adb shell setprop persist.log.tag E")
    os.system("adb shell setprop persist.log.tag.DualCamDepthPreviewEngine V")

def logOff():
    print("关闭log")
    os.system("adb shell setprop persist.camera.bokeh.preview.log.status 0")
    os.system("adb shell setprop rw.hcs.log.level 5") # 关闭hcs log
    os.system("adb shell setprop persist.log.tag F")
    os.system("adb shell setprop persist.log.tag.DualCamDepthPreviewEngine F")

def traceOff():
    print("关闭trace")
    os.system("adb shell setprop persist.camera.bokeh.preview.enable.perf.trace 0")
    os.system("adb shell setprop rw.hcs.atrace.enable 0")

### main() ###
os.system("adb root")
os.system("adb shell setenforce 0")
os.system("adb logcat -G 64M")
os.system("adb shell setprop persist.logd.flowctrl.quota.rows 65535")
closeDump()

# trace log
traceOff()
logOn()

#os.system("adb shell kill `pidof vendor.qti.camera.provider-service_64`")
print("done")
