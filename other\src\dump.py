#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dump management module for Bokeh Verification Tool.

This module handles configuring and managing dump settings on the device.
"""

import os
import logging
from typing import List, Dict, Optional, Any

from config import Config
from utils import run_adb_command

class DumpManager:
    """Manager for Bokeh dump settings."""

    def __init__(self, config: Config):
        """
        Initialize the dump manager.

        Args:
            config: Configuration object.
        """
        self.logger = logging.getLogger('bokeh_verify.dump')
        self.config = config

        # Device configuration
        self.device_id = config.get('device', 'id')

        # Dump configuration
        self.dump_base_path = config.get('dump', 'dump_base_path')
        self.dump_local_path = config.get('dump', 'dump_local_path')

        # Dump levels
        self.depth_dump_level = config.get('dump', 'depth_dump_level')
        self.render_dump_level = config.get('dump', 'render_dump_level')
        self.rectify_dump_level = config.get('dump', 'rectify_dump_level')
        self.depth_warp_dump_level = config.get('dump', 'depth_warp_dump_level')
        self.create_new_dir = config.get('dump', 'create_new_dir')

        # Module mapping - 根据你提供的实际属性名称
        self.module_property_map = {
            'depth': 'persist.camera.bokeh.depthcap',  # depth模块dump开关
            'rectify': 'persist.camera.bokeh.rectify.dump',  # rectify模块dump开关
            'render': 'persist.camera.bokeh.render.dump.level',  # render模块dump级别
            'depth_warp': 'persist.camera.bokeh.dump.depth.warp',  # depth_warp模块
            'all': 'persist.camera.bokeh.dump.level',  # 全局dump级别
        }

    def enable_module_dump(self, module: str, level: str = '3') -> bool:
        """
        Enable dump for a specific module.
        启用特定模块的dump功能。

        Args / 参数:
            module: Module name (depth, render, rectify, depth_warp, all) / 模块名称
            level: Dump level (0-3) or '1' for on/off switches / dump级别或开关值

        Returns / 返回:
            True if successful, False otherwise / 如果成功则返回True，否则返回False
        """
        if module not in self.module_property_map:
            self.logger.error(f"Unknown module / 未知模块: {module}")
            return False

        property_name = self.module_property_map[module]

        # 对于depth和rectify模块，使用开关值（1=开启，0=关闭）
        if module in ['depth', 'rectify']:
            value = '1' if level != '0' else '0'
            action = "开启" if value == '1' else "关闭"
            self.logger.info(f"{action} {module} 模块dump / {action} dump for {module} module")
        else:
            value = level
            self.logger.info(f"设置 {module} 模块dump级别为 {level} / Setting dump level {level} for {module} module")

        return_code, _, stderr = run_adb_command(
            f"shell setprop {property_name} {value}",
            device_id=self.device_id
        )

        if return_code != 0:
            self.logger.error(f"Failed to enable dump for module {module}: {stderr}")
            return False

        return True

    def disable_module_dump(self, module: str) -> bool:
        """
        Disable dump for a specific module.

        Args:
            module: Module name (depth, render, rectify, depth_warp, all).

        Returns:
            True if successful, False otherwise.
        """
        return self.enable_module_dump(module, '0')

    def enable_default_dumps(self) -> bool:
        """
        Enable default dumps - 2个大模块的最终输出.
        启用默认dump - 2个大模块的最终输出。

        Returns / 返回:
            True if all dumps were enabled successfully / 如果所有dump都成功启用则返回True
        """
        self.logger.info("启用默认dump（2个大模块最终输出）/ Enabling default dumps (2 main modules final output)...")

        # Create dump directory / 创建dump目录
        run_adb_command(
            f"shell mkdir -p {self.dump_base_path}",
            device_id=self.device_id
        )

        # 默认只启用2个大模块的最终输出，不启用子模块
        # By default, only enable final output of 2 main modules, not sub-modules
        success = True

        # 确保子模块dump是关闭的（默认状态）
        # Ensure sub-module dumps are disabled (default state)
        success &= self.disable_module_dump('depth')  # 关闭depth子模块dump
        success &= self.disable_module_dump('rectify')  # 关闭rectify子模块dump

        # 设置主要模块的dump级别（如果配置中有指定）
        # Set main module dump levels (if specified in config)
        if hasattr(self, 'render_dump_level') and self.render_dump_level:
            success &= self.enable_module_dump('render', self.render_dump_level)

        # Set create new directory option / 设置创建新目录选项
        return_code, _, stderr = run_adb_command(
            f"shell setprop persist.camera.bokeh.dump.create.newdir {self.create_new_dir}",
            device_id=self.device_id
        )

        if return_code != 0:
            self.logger.error(f"Failed to set create new directory option: {stderr}")
            success = False

        self.logger.info("默认dump配置完成 / Default dump configuration completed")
        return success

    def disable_all_dumps(self) -> bool:
        """
        Disable all dumps.

        Returns:
            True if all dumps were disabled successfully, False otherwise.
        """
        self.logger.info("Disabling all dumps...")

        success = True
        for module in self.module_property_map:
            success &= self.disable_module_dump(module)

        return success

    def get_dump_status(self) -> Dict[str, str]:
        """
        Get the current dump status.

        Returns:
            Dictionary mapping module names to their current dump levels.
        """
        self.logger.info("Getting dump status...")

        status = {}
        for module, property_name in self.module_property_map.items():
            _, stdout, _ = run_adb_command(
                f"shell getprop {property_name}",
                device_id=self.device_id
            )
            status[module] = stdout.strip() or '0'

        return status

    def enable_memory_trace(self, enable: bool = True) -> bool:
        """
        Enable or disable memory trace.

        Args:
            enable: Whether to enable memory trace.

        Returns:
            True if successful, False otherwise.
        """
        value = '1' if enable else '0'
        self.logger.info(f"{'Enabling' if enable else 'Disabling'} memory trace...")

        return_code, _, stderr = run_adb_command(
            f"shell setprop persist.camera.bokeh.enable.memory.trace {value}",
            device_id=self.device_id
        )

        if return_code != 0:
            self.logger.error(f"Failed to {'enable' if enable else 'disable'} memory trace: {stderr}")
            return False

        return True

    def enable_log_trace(self, enable: bool = True) -> bool:
        """
        Enable or disable log trace.

        Args:
            enable: Whether to enable log trace.

        Returns:
            True if successful, False otherwise.
        """
        value = '63' if enable else '0'
        self.logger.info(f"{'Enabling' if enable else 'Disabling'} log trace...")

        # Increase logcat buffer size
        if enable:
            run_adb_command(
                "logcat -G 64M",
                device_id=self.device_id
            )

        return_code, _, stderr = run_adb_command(
            f"shell setprop persist.camera.bokeh.log.status {value}",
            device_id=self.device_id
        )

        if return_code != 0:
            self.logger.error(f"Failed to {'enable' if enable else 'disable'} log trace: {stderr}")
            return False

        return True

    def clear_dump_directory(self) -> bool:
        """
        Clear the dump directory on the device.

        Returns:
            True if successful, False otherwise.
        """
        self.logger.info(f"Clearing dump directory: {self.dump_base_path}")

        return_code, _, stderr = run_adb_command(
            f"shell rm -rf {self.dump_base_path}/*",
            device_id=self.device_id
        )

        if return_code != 0:
            self.logger.error(f"Failed to clear dump directory: {stderr}")
            return False

        # Recreate the directory
        run_adb_command(
            f"shell mkdir -p {self.dump_base_path}",
            device_id=self.device_id
        )

        return True
