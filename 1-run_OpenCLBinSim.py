# -*- coding: utf-8 -*-
#########################################################
## Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd.
##
## File: - 3-run_OpenCLBinSim.py
## Description: build sdk and pack sdk
## Version: 1.0
## Date : 2024/09/07
## Author: dxd
## ----------------------Revision History: --------------------
##  <author>    <data>    <version >     <desc>
##  dxd       2024/09/07    1.0       creat init file
#########################################################
import os
import filecmp
import sys
import shutil
import argparse
sys.path.append('../ArcBokehSim')
import common

devices_id = common.get_device_id()
if len(devices_id) == 1:
    print("Find devices:", devices_id[0])
    devices = devices_id[0]
elif len(devices_id) > 1:
    print("Find devices, please select one: ", devices_id)
    print("Multi devices, default use first one: ", devices_id[0])
    devices = devices_id[0]
else:
    print("NO devices found")
    exit(1)

print("Remounting device...")
command = "adb -s " + devices + " remount"
os.system(command)

command = "adb -s " + devices + " shell rm -rf /data/local/tmp/BokehOCLSim"
os.system(command)

command = "adb -s " + devices + " shell mkdir -p /data/local/tmp/BokehOCLSim/"
os.system(command)

command = "adb -s " + devices + " push libs/arm64-v8a/libOPAlgoCamCaptureDualPortrait.so /odm/lib64/"
os.system(command)

command = "adb -s " + devices + " push obj/local/arm64-v8a/Test_OpenCLBinSim /data/local/tmp/BokehOCLSim/"
os.system(command)

command = "adb -s " + devices + " shell chmod 777 /data/local/tmp/BokehOCLSim/Test_OpenCLBinSim"
os.system(command)

command = 'adb -s ' + devices + ' shell "rm /odm/etc/camera/dualcam_capture_bokeh/dualcam_cl_*"'
os.system(command)
print("remove old clBin ", command)

command = 'adb -s ' + devices + ' shell "cd /data/local/tmp/BokehOCLSim && ./Test_OpenCLBinSim"'
os.system(command)

command = "adb -s " + devices + " shell ls -lh /odm/etc/camera/dualcam_capture_bokeh/dualcam_cl_*"
p = os.popen(command)
clbin_name = p.read()
# clbin_name = os.system(command)
clbin_name = clbin_name.split(' ')[-1].replace('\n', '').replace('\r', '')
print("clbin_name: ", clbin_name)

command = "adb -s " + devices + " pull " + clbin_name
os.system(command)

try:
    clbin_name_local = clbin_name.split('/')[-1]
    print("clbin_name_local ", clbin_name_local)
    OpenCLBinary_dir = "./OpenCLBinary/"
    clbin_new = clbin_name_local
    clbin_old = OpenCLBinary_dir + clbin_name_local
    if not os.path.exists(OpenCLBinary_dir):
        os.mkdir(OpenCLBinary_dir)
        shutil.copy(clbin_new, OpenCLBinary_dir)
        os.remove(clbin_new)
        exit(1)
    if not os.path.exists(clbin_old):
        shutil.copy(clbin_new, OpenCLBinary_dir)
        os.remove(clbin_new)
        exit(1)
    status = filecmp.cmp(clbin_new, clbin_old)
    if status:
        print("Generated CLBinary is same as old one. skip copy")
    else:
        print("Generated CLBinary is different from old one. copy to OpenCLBinary")
        shutil.copy(clbin_new, OpenCLBinary_dir)
    os.remove(clbin_new)
except IOError:
    print("Error: File not found or failed to read ", clbin_new, clbin_old)
