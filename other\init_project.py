#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Initialization script for Bokeh Verification Tool.

This script sets up the project for first-time users.
"""

import os
import sys
import shutil
import argparse
from typing import List, Dict, Any

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Initialize Bokeh Verification Tool')
    
    parser.add_argument('--config', action='store_true', help='Create config.ini from template')
    parser.add_argument('--dirs', action='store_true', help='Create required directories')
    parser.add_argument('--all', action='store_true', help='Perform all initialization steps')
    
    return parser.parse_args()

def create_config():
    """Create config.ini from template."""
    if os.path.exists('config.ini'):
        print("config.ini already exists. Skipping.")
        return
    
    if not os.path.exists('config.ini.template'):
        print("config.ini.template not found. Cannot create config.ini.")
        return
    
    shutil.copy('config.ini.template', 'config.ini')
    print("Created config.ini from template.")

def create_directories():
    """Create required directories."""
    directories = [
        'verify_results',
        'verify_data/golden',
        'outputs/vbdump',
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")
        else:
            print(f"Directory already exists: {directory}")

def main():
    """Main entry point."""
    args = parse_arguments()
    
    if args.all or args.config:
        create_config()
    
    if args.all or args.dirs:
        create_directories()
    
    print("Initialization complete.")
    print("Next steps:")
    print("1. Edit config.ini with your specific settings")
    print("2. Install required dependencies: pip install -r requirements.txt")
    print("3. Run the verification tool: python bokeh_verify.py")

if __name__ == "__main__":
    main()
