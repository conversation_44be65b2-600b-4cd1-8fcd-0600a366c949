# Bokeh Verification Tool Configuration Template
# Bokeh验证工具配置模板
# Copy this file and modify according to your environment
# 复制此文件并根据您的环境进行修改

[build]
# Build configuration / 构建配置
auto_build = true
auto_push = true
series = X8U
lib_type = 0
clean = false

# SDK path on remote server (for SSH builds) / 远程服务器上的SDK路径（用于SSH构建）
sdk_path = ~/zx_code/bokeh/oppo_dualDepth/DualCamDepthEngine/jni/ArcBokehSim

# Local build paths (for local builds) / 本地构建路径（用于本地构建）
ndk_path = 
project_path = 

[device]
# Device configuration / 设备配置
# Leave device_id empty for auto-detection / 留空device_id以自动检测
device_id = 

# SSH configuration for remote builds / SSH配置用于远程构建
ssh_host = *************
ssh_user = 80398453
ssh_port = 22
ssh_password = 
ssh_key_file = 

# Device paths / 设备路径
lib_path = /odm/lib64
test_program_path = /data/local/tmp/BokehSim

[dump]
# Dump configuration / Dump配置
# Sub-module dump path on device / 设备上的子模块dump路径
dump_base_path = /sdcard/Android/data/com.oplus.camera/files/spdebug/bokehdump

# Default dump levels / 默认dump级别
depth_dump_level = 0
render_dump_level = 3
rectify_dump_level = 0
depth_warp_dump_level = 0

# Create new directory for each dump / 为每次dump创建新目录
create_new_dir = 0

[golden]
# Golden data configuration / Golden数据配置
# Base directory for golden data storage / Golden数据存储基础目录
base_dir = golden_data

[compare]
# Comparison configuration / 对比配置
# Reference directory for comparison (will use golden data if available) / 对比参考目录（如果可用将使用golden数据）
reference_dir = golden_data/current

# Comparison thresholds / 对比阈值
pixel_threshold = 0
similarity_threshold = 0.95

[output]
# Output configuration / 输出配置
dir = verify_results

[logging]
# Logging configuration / 日志配置
level = INFO
format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
