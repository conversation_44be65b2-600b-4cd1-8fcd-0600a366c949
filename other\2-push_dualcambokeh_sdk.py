# -*- coding: utf-8 -*-
#########################################################
## Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd.
##
## File: - buil_dualcambokeh_sdk.py
## Description: build sdk and pack sdk
## Version: 1.0
## Date : 2024/09/07
## Author: dxd
## ----------------------Revision History: --------------------
##  <author>    <data>    <version >     <desc>
##  dxd       2024/09/07    1.0       creat init file
#########################################################
import os
import shutil
import common
import argparse

seriesMap = common.SERIES_MAP
productMap = common.PRODUCT_MAP
findAllfile = common.find_all_files

parser = argparse.ArgumentParser(description='Push DualCamBokeh SDK to device')
parser.add_argument('-s', '--series', type=str, required=False, choices=list(seriesMap.keys()), default=common.DEFAULT_SERIES,
                   help='Device series: X7U, X8, X8U, X9, X9U (default: %(default)s)')
parser.add_argument('-p', '--product', type=str, required=False, choices=list(productMap.keys()), default=common.DEFAULT_PRODUCT,
                   help='Product name: zhufeng, yala, huanghe (default: %(default)s)')
args = parser.parse_args()

soc_config = common.get_soc_config(args.series)
TARGET_SOC = soc_config["TARGET_SOC"]
SERIES = soc_config["SERIES"]
PROJECT = soc_config["PROJECT"]
ODNN_VERSION = soc_config["ODNN_VERSION"]
QNN_VERSION = soc_config["QNN_VERSION"]
AI_SDK = soc_config["AI_SDK"]
VENDOR = soc_config["VENDOR"]
MTK_SOC = soc_config.get("MTK_SOC", "")
CL_BINARY = soc_config["CL_BINARY"]

print("SOC argument: ", TARGET_SOC)
print("Series argument: ", SERIES)
print("PROJECT argument: ", PROJECT)
print("CL_BINARY argument: ", CL_BINARY)

#step 1 get devices
devices_id = common.get_device_id()
if len(devices_id) == 1:
    print("Find devices:", devices_id[0])
    devices = devices_id[0]
elif len(devices_id) > 1:
    print("Find devices, please select one: ", devices_id)
    print("Multi devices, default use first one: ", devices_id[0])
    devices = devices_id[0]
else:
    print("NO devices found")
    exit(1)

# Step 1: push AI models
print("Remounting device...")
command = "adb -s " + devices + " remount"
os.system(command)

print("Pushing model files...")
model_file_path = os.path.join(common.THIRDPARTY_AIMODEL_PATH, TARGET_SOC + "_ODNN_" + ODNN_VERSION + "_" + AI_SDK + "_" + QNN_VERSION)
print("Model file path: ", model_file_path)
if os.path.exists(model_file_path) is not True:
    print("model_file_path does not exist!")
    os._exit()

# 创建目标目录
command = "adb -s " + devices + " shell mkdir -p /odm/etc/camera/dualcam_capture_bokeh"
os.system(command)

# 推送所有模型文件
all_files = findAllfile(model_file_path, [])
for file_path in all_files:
    command = "adb -s " + devices + " push " + file_path + " /odm/etc/camera/dualcam_capture_bokeh"
    os.system(command)

# Step 2: push libs and binaries
print("\nPushing library and binary files...")
lib_path = os.path.join("libs", common.TARGET_ARCH_ABI, common.LIB_NAME)
json_source_dir = os.path.join(common.THIRDPARTY_RECTIFY_PATH, args.series, args.product, 
                              common.RECTIFY_PARAM_JSON_NAME)
json_source_dir = os.path.normpath(json_source_dir)

# 推送库文件和二进制文件
command = "adb -s " + devices + " push " + lib_path + " /odm/lib64/"
os.system(command)
command = "adb -s " + devices + " push " + os.path.join(common.CL_BINARY_DIR, CL_BINARY) + " /odm/etc/camera/dualcam_capture_bokeh/"
os.system(command)
command = "adb -s " + devices + " push " + json_source_dir + " /odm/etc/camera/dualcam_capture_bokeh/"
os.system(command)

# Step 3: 重启相机服务
print("\nRestarting camera services...")
command = "adb -s " + devices + " shell ps | findstr camera"
os.system(command)
command = "adb -s " + devices + " pkill camera*"
os.system(command)
print("Push completed successfully.")