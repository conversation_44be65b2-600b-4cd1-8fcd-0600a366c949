#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSH connection test script for Bokeh verification tool.
Bokeh验证工具的SSH连接测试脚本。

This script tests SSH connection to the remote Linux server.
该脚本测试到远程Linux服务器的SSH连接。
"""

import os
import sys
import logging

# Add src directory to path / 将src目录添加到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    import paramiko
    PARAMIKO_AVAILABLE = True
except ImportError:
    PARAMIKO_AVAILABLE = False
    print("❌ paramiko not available. Please install: pip install paramiko")
    sys.exit(1)

from config import Config
from utils import setup_logging

def test_ssh_connection(config: Config) -> bool:
    """
    Test SSH connection to remote server.
    测试到远程服务器的SSH连接。
    
    Args / 参数:
        config: Configuration object / 配置对象
        
    Returns / 返回:
        True if connection successful / 如果连接成功则返回True
    """
    logger = logging.getLogger('ssh_test')
    
    # Get SSH configuration / 获取SSH配置
    ssh_host = config.get('device', 'ssh_host', '')
    ssh_user = config.get('device', 'ssh_user', '')
    ssh_port = config.getint('device', 'ssh_port', 22)
    ssh_password = config.get('device', 'ssh_password', '')
    ssh_key_file = config.get('device', 'ssh_key_file', '')
    
    if not ssh_host or not ssh_user:
        logger.error("❌ SSH host or user not configured / SSH主机或用户未配置")
        logger.error("Please configure ssh_host and ssh_user in config file")
        logger.error("请在配置文件中配置ssh_host和ssh_user")
        return False
    
    logger.info(f"🔗 Testing SSH connection to {ssh_user}@{ssh_host}:{ssh_port}")
    logger.info(f"🔗 测试SSH连接到 {ssh_user}@{ssh_host}:{ssh_port}")
    
    try:
        # Create SSH client / 创建SSH客户端
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        # Connect using different authentication methods / 使用不同的认证方法连接
        if ssh_key_file and os.path.exists(ssh_key_file):
            logger.info(f"🔑 Using SSH key authentication: {ssh_key_file}")
            logger.info(f"🔑 使用SSH密钥认证: {ssh_key_file}")
            ssh.connect(ssh_host, port=ssh_port, username=ssh_user, key_filename=ssh_key_file)
        elif ssh_password:
            logger.info("🔒 Using password authentication")
            logger.info("🔒 使用密码认证")
            ssh.connect(ssh_host, port=ssh_port, username=ssh_user, password=ssh_password)
        else:
            logger.info("🔓 Trying passwordless authentication")
            logger.info("🔓 尝试无密码认证")
            ssh.connect(ssh_host, port=ssh_port, username=ssh_user)
        
        # Test basic command / 测试基本命令
        logger.info("📋 Testing basic command: pwd")
        logger.info("📋 测试基本命令: pwd")
        stdin, stdout, stderr = ssh.exec_command('pwd')
        exit_status = stdout.channel.recv_exit_status()
        
        if exit_status == 0:
            current_dir = stdout.read().decode('utf-8').strip()
            logger.info(f"✅ SSH connection successful! Current directory: {current_dir}")
            logger.info(f"✅ SSH连接成功！当前目录: {current_dir}")
        else:
            error_msg = stderr.read().decode('utf-8').strip()
            logger.error(f"❌ Command failed: {error_msg}")
            logger.error(f"❌ 命令失败: {error_msg}")
            ssh.close()
            return False
        
        # Test SDK path / 测试SDK路径
        sdk_path = config.get('build', 'sdk_path', '')
        if sdk_path:
            logger.info(f"📁 Testing SDK path: {sdk_path}")
            logger.info(f"📁 测试SDK路径: {sdk_path}")
            stdin, stdout, stderr = ssh.exec_command(f'ls -la {sdk_path}')
            exit_status = stdout.channel.recv_exit_status()
            
            if exit_status == 0:
                logger.info("✅ SDK path accessible")
                logger.info("✅ SDK路径可访问")
                
                # Check for build script / 检查构建脚本
                build_script = config.get('build', 'build_script', '1-build_dualcambokeh_sdk.py')
                stdin, stdout, stderr = ssh.exec_command(f'ls -la {sdk_path}/{build_script}')
                exit_status = stdout.channel.recv_exit_status()
                
                if exit_status == 0:
                    logger.info(f"✅ Build script found: {build_script}")
                    logger.info(f"✅ 找到构建脚本: {build_script}")
                else:
                    logger.warning(f"⚠️  Build script not found: {build_script}")
                    logger.warning(f"⚠️  未找到构建脚本: {build_script}")
            else:
                logger.warning(f"⚠️  SDK path not accessible: {sdk_path}")
                logger.warning(f"⚠️  SDK路径不可访问: {sdk_path}")
        
        ssh.close()
        return True
        
    except paramiko.AuthenticationException:
        logger.error("❌ SSH authentication failed / SSH认证失败")
        logger.error("Please check your username, password, or SSH key")
        logger.error("请检查您的用户名、密码或SSH密钥")
        return False
    except paramiko.SSHException as e:
        logger.error(f"❌ SSH connection error / SSH连接错误: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error / 意外错误: {str(e)}")
        return False

def main():
    """
    Main entry point.
    主入口点。
    """
    # Setup logging / 设置日志
    setup_logging(logging.INFO)
    logger = logging.getLogger('ssh_test')
    
    logger.info("🧪 SSH Connection Test / SSH连接测试")
    logger.info("=" * 50)
    
    # Check if config file exists / 检查配置文件是否存在
    config_files = ['config.ssh.ini', 'config.simple.ini', 'config.ini']
    config_file = None
    
    for cf in config_files:
        if os.path.exists(cf):
            config_file = cf
            break
    
    if not config_file:
        logger.error("❌ No configuration file found")
        logger.error("❌ 未找到配置文件")
        logger.error("Please create one of: config.ssh.ini, config.simple.ini, config.ini")
        logger.error("请创建以下文件之一: config.ssh.ini, config.simple.ini, config.ini")
        return 1
    
    logger.info(f"📄 Using configuration file: {config_file}")
    logger.info(f"📄 使用配置文件: {config_file}")
    
    # Load configuration / 加载配置
    config = Config(config_file)
    
    # Test SSH connection / 测试SSH连接
    success = test_ssh_connection(config)
    
    # Summary / 总结
    logger.info("\n" + "=" * 50)
    if success:
        logger.info("🎉 SSH connection test PASSED! / SSH连接测试通过！")
        logger.info("You can now use SSH build functionality")
        logger.info("您现在可以使用SSH构建功能")
        return 0
    else:
        logger.error("❌ SSH connection test FAILED! / SSH连接测试失败！")
        logger.error("Please check your SSH configuration")
        logger.error("请检查您的SSH配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
