#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Common module for Bokeh Verification Tool / Bokeh验证工具通用模块

This module contains common configurations and utility functions for Bokeh SDK.
该模块包含Bokeh SDK的通用配置和工具函数。
"""

import os
import platform
import subprocess
from datetime import datetime
from typing import List, Dict, Any, Optional

# Default configurations / 默认配置
DEFAULT_SERIES = "X8U"
DEFAULT_PRODUCT = "huanghe"
DEFAULT_LIB_TYPE = 0  # 0=normal, 1=coverity, 2=asan
DEFAULT_CLEAN_TYPE = 0  # 0=false, 1=true
DEFAULT_NEED_COPY_ETC = 0  # 0=false, 1=true
DEFAULT_SDK_VERSION = "1.0.0"

# NDK configurations / NDK配置
NDK_PATH = "ndk-build"  # Assume ndk-build is in PATH / 假设ndk-build在PATH中
TARGET_ARCH_ABI = "arm64-v8a"
NDK_PROJECT_PATH = "."
APP_BUILD_SCRIPT = "Android.mk"
NDK_APPLICATION_MK = "Application.mk"

# Library configurations / 库配置
LIB_NAME = "libOPAlgoCamCaptureDualPortrait.so"

# Path configurations / 路径配置
SDK_RELEASE_BASE_DIR = "sdk_release"
THIRDPARTY_AIMODEL_PATH = "3rdparty/aimodel"  # 相对于SDK根目录
THIRDPARTY_RECTIFY_PATH = "3rdparty/rectify"  # 相对于SDK根目录
CL_BINARY_DIR = "OpenCLBinary"  # 相对于SDK根目录
INTERFACE_ASADAPTER_INCLUDE_PATH = "interface/AsAdapter/include"

# File names / 文件名
COMMIT_ID_FILENAME = "commit_id.txt"
COMMIT_INFO_FILENAME = "commit_info.txt"
UNLOCK_BAT_FILENAME = "1_unlock.bat"
PUSHLIB_BAT_FILENAME = "2_pushlib.bat"
GETLOG_BAT_FILENAME = "3_GetLog.bat"
COPY_TO_APS_FILENAME = "4_CopyToAps.bat"
GRADLE_RELEASE_NOTE = "gradle_release_note.txt"
RECTIFY_PARAM_JSON_NAME = "rectify_param.json"
HEADER_BOKEH = "OPAlgoCamCaptureDualPortrait.h"
HEADER_BOKEH_COMMON = "OPAlgoCamCaptureDualPortraitCommon.h"

# Series and product mappings / 系列和产品映射
SERIES_MAP = {
    "X8U": {
        "projects": ["huanghe", "zhujiang", "changjiang"]
    },
    "X9": {
        "projects": ["changjiang", "zhujiang"]
    },
    "X9U": {
        "projects": ["changjiang", "zhujiang"]
    }
}

PRODUCT_MAP = {
    "huanghe": "huanghe",
    "zhujiang": "zhujiang",
    "changjiang": "changjiang"
}

# Device product mapping / 设备产品映射
DEVICE_PRODUCT_MAP = {
    "huanghe": ["CPH2609"],
    "zhujiang": ["CPH2607"],
    "changjiang": ["CPH2605"]
}

def get_soc_config(series: str) -> Dict[str, Any]:
    """
    Get SOC configuration for a given series.
    获取给定系列的SOC配置。

    Args / 参数:
        series: Device series / 设备系列

    Returns / 返回:
        Dictionary with SOC configuration / SOC配置字典
    """
    configs = {
        "X8U": {
            "TARGET_SOC": "SM8550",
            "SERIES": "X8U",
            "PROJECT": ["huanghe", "zhujiang", "changjiang"],
            "ODNN_VERSION": "2.25.0",
            "QNN_VERSION": "2.25.0",
            "AI_SDK": "QNN",
            "VENDOR": "QCOM",
            "MTK_SOC": "",
            "CL_BINARY": "dualcam_cl_SM8550.bin",
            "APS_PATH": "\\algorithms\\oppo_dualDepth_capture\\DualDepthCapture\\jni\\ArcBokehSim",
            "ETC_PATH": None
        },
        "X9": {
            "TARGET_SOC": "SM8650",
            "SERIES": "X9",
            "PROJECT": ["changjiang", "zhujiang"],
            "ODNN_VERSION": "2.28.0",
            "QNN_VERSION": "2.28.0",
            "AI_SDK": "QNN",
            "VENDOR": "QCOM",
            "MTK_SOC": "",
            "CL_BINARY": "dualcam_cl_SM8650.bin",
            "APS_PATH": "\\algorithms\\oppo_dualDepth_capture\\DualDepthCapture\\jni\\ArcBokehSim",
            "ETC_PATH": None
        },
        "X9U": {
            "TARGET_SOC": "SM8650",
            "SERIES": "X9U",
            "PROJECT": ["changjiang", "zhujiang"],
            "ODNN_VERSION": "2.28.0",
            "QNN_VERSION": "2.28.0",
            "AI_SDK": "QNN",
            "VENDOR": "QCOM",
            "MTK_SOC": "",
            "CL_BINARY": "dualcam_cl_SM8650.bin",
            "APS_PATH": "\\algorithms\\oppo_dualDepth_capture\\DualDepthCapture\\jni\\ArcBokehSim",
            "ETC_PATH": None
        }
    }

    return configs.get(series, configs[DEFAULT_SERIES])

def get_device_id() -> List[str]:
    """
    Get connected device IDs.
    获取已连接的设备ID。

    Returns / 返回:
        List of device IDs / 设备ID列表
    """
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
        lines = result.stdout.strip().split('\n')[1:]  # Skip header
        devices = []
        for line in lines:
            if line and '\tdevice' in line:
                devices.append(line.split('\t')[0])
        return devices
    except Exception as e:
        print(f"Error getting device IDs / 获取设备ID时出错: {e}")
        return []

def get_device_product() -> Dict[str, str]:
    """
    Get device product information.
    获取设备产品信息。

    Returns / 返回:
        Dictionary mapping device ID to product name / 设备ID到产品名称的映射字典
    """
    devices = get_device_id()
    device_products = {}

    for device in devices:
        try:
            result = subprocess.run(['adb', '-s', device, 'shell', 'getprop', 'ro.product.model'],
                                  capture_output=True, text=True)
            product = result.stdout.strip()
            device_products[device] = product
        except Exception as e:
            print(f"Error getting product for device {device} / 获取设备{device}产品信息时出错: {e}")

    return device_products

def get_build_sdk_time() -> str:
    """
    Get current timestamp for SDK build.
    获取SDK构建的当前时间戳。

    Returns / 返回:
        Timestamp string / 时间戳字符串
    """
    return datetime.now().strftime('%Y%m%d%H%M%S')

def get_platform() -> str:
    """
    Get current platform.
    获取当前平台。

    Returns / 返回:
        Platform name / 平台名称
    """
    return platform.system()

def check_ndk_version() -> bool:
    """
    Check if NDK is available.
    检查NDK是否可用。

    Returns / 返回:
        True if NDK is available / 如果NDK可用则返回True
    """
    try:
        result = subprocess.run(['ndk-build', '--version'], capture_output=True, text=True)
        return result.returncode == 0
    except Exception:
        print("NDK not found in PATH / 在PATH中未找到NDK")
        return False

def get_ndk_version() -> str:
    """
    Get NDK version.
    获取NDK版本。

    Returns / 返回:
        NDK version string / NDK版本字符串
    """
    try:
        result = subprocess.run(['ndk-build', '--version'], capture_output=True, text=True)
        return result.stdout.strip()
    except Exception:
        return "Unknown"

def prop_ndkBuild_ndkVersion() -> str:
    """
    Get NDK build version property.
    获取NDK构建版本属性。

    Returns / 返回:
        NDK build version / NDK构建版本
    """
    return get_ndk_version()

def get_clean_flag(clean_type: int) -> str:
    """
    Get clean flag based on clean type.
    根据清理类型获取清理标志。

    Args / 参数:
        clean_type: Clean type (0 or 1) / 清理类型（0或1）

    Returns / 返回:
        Clean flag string / 清理标志字符串
    """
    return "true" if clean_type == 1 else "false"

def mkdir_if_not_exists(path: str) -> bool:
    """
    Create directory if it doesn't exist.
    如果目录不存在则创建。

    Args / 参数:
        path: Directory path / 目录路径

    Returns / 返回:
        True if successful / 如果成功则返回True
    """
    try:
        os.makedirs(path, exist_ok=True)
        return True
    except Exception as e:
        print(f"Error creating directory {path} / 创建目录{path}时出错: {e}")
        return False

def check_file_exists(path: str) -> bool:
    """
    Check if file or directory exists.
    检查文件或目录是否存在。

    Args / 参数:
        path: File or directory path / 文件或目录路径

    Returns / 返回:
        True if exists / 如果存在则返回True
    """
    return os.path.exists(path)

def find_all_files(directory: str, file_list: List[str]) -> List[str]:
    """
    Find all files in a directory recursively.
    递归查找目录中的所有文件。

    Args / 参数:
        directory: Directory to search / 要搜索的目录
        file_list: List to append files to / 要添加文件的列表

    Returns / 返回:
        List of file paths / 文件路径列表
    """
    if not os.path.exists(directory):
        return file_list

    for root, dirs, files in os.walk(directory):
        for file in files:
            file_list.append(os.path.join(root, file))

    return file_list

def get_key(dictionary: Dict[str, Any], value: Any) -> Optional[str]:
    """
    Get key by value in dictionary.
    根据值在字典中获取键。

    Args / 参数:
        dictionary: Dictionary to search / 要搜索的字典
        value: Value to find / 要查找的值

    Returns / 返回:
        Key if found, None otherwise / 如果找到则返回键，否则返回None
    """
    for k, v in dictionary.items():
        if v == value or (isinstance(v, list) and value in v):
            return k
    return None
