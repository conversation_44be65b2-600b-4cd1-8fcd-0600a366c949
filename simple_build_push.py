#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple build and push script for Bokeh SDK.
Bokeh SDK的简单构建和推送脚本。

This script provides a minimal implementation without complex dependencies.
该脚本提供不依赖复杂依赖项的最小实现。
"""

import os
import sys
import subprocess
import time
import logging

def setup_logging():
    """Setup basic logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    return logging.getLogger('simple_build_push')

def run_command(cmd, cwd=None):
    """Run a command and return the result."""
    logger = logging.getLogger('simple_build_push')
    logger.info(f"Running: {cmd}")

    try:
        result = subprocess.run(
            cmd,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            logger.error(f"Command failed: {result.stderr}")
            return False, result.stderr

        logger.debug(f"Output: {result.stdout}")
        return True, result.stdout

    except Exception as e:
        logger.error(f"Error running command: {e}")
        return False, str(e)

def check_device():
    """Check if device is connected."""
    logger = logging.getLogger('simple_build_push')
    logger.info("🔍 Checking device connection...")

    success, output = run_command("adb devices")
    if not success:
        return None

    lines = output.strip().split('\n')[1:]  # Skip header
    devices = [line.split('\t')[0] for line in lines if line and '\tdevice' in line]

    if not devices:
        logger.error("❌ No device connected")
        return None

    device_id = devices[0]
    logger.info(f"✅ Device found: {device_id}")
    return device_id

def check_ndk():
    """Check if NDK is available."""
    logger = logging.getLogger('simple_build_push')
    logger.info("🔍 Checking NDK...")

    try:
        result = subprocess.run(['ndk-build', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ NDK found")
            return True
        else:
            logger.error("❌ NDK not working properly")
            return False
    except FileNotFoundError:
        logger.error("❌ NDK not found in PATH")
        logger.error("Please install Android NDK and add ndk-build to your PATH")
        logger.error("请安装Android NDK并将ndk-build添加到PATH中")
        return False

def build_bokeh():
    """Build Bokeh SDK."""
    logger = logging.getLogger('simple_build_push')
    logger.info("🔨 Building Bokeh SDK...")

    # Check if build script exists
    build_script = "1-build_dualcambokeh_sdk.py"
    if not os.path.exists(build_script):
        logger.error(f"❌ Build script not found: {build_script}")
        return False

    # Check NDK
    if not check_ndk():
        return False

    # Run build command
    build_cmd = f"python {build_script} -s X8U -l 0 -c 0"
    success, output = run_command(build_cmd)

    if not success:
        logger.error("❌ Build failed")
        logger.error("Build output:")
        logger.error(output)
        return False

    # Check if library was built
    lib_path = "libs/arm64-v8a/libOPAlgoCamCaptureDualPortrait.so"
    if not os.path.exists(lib_path):
        logger.error(f"❌ Library not found: {lib_path}")
        return False

    file_size = os.path.getsize(lib_path) / (1024 * 1024)  # MB
    logger.info(f"✅ Build successful: {lib_path} ({file_size:.1f} MB)")
    return True

def push_to_device(device_id):
    """Push library to device."""
    logger = logging.getLogger('simple_build_push')
    logger.info("📱 Pushing to device...")

    # Prepare device
    logger.info("Preparing device...")
    run_command(f"adb -s {device_id} root")
    time.sleep(1)
    run_command(f"adb -s {device_id} remount")
    run_command(f"adb -s {device_id} shell setenforce 0")

    # Push main library
    lib_path = "libs/arm64-v8a/libOPAlgoCamCaptureDualPortrait.so"
    target_path = "/odm/lib64/libOPAlgoCamCaptureDualPortrait.so"

    logger.info(f"Pushing library: {lib_path} -> {target_path}")
    success, output = run_command(f"adb -s {device_id} push {lib_path} {target_path}")

    if not success:
        logger.error("❌ Failed to push library")
        return False

    # Restart camera service
    logger.info("Restarting camera service...")
    run_command(f"adb -s {device_id} shell pkill camera*")

    logger.info("✅ Push successful")
    return True

def main():
    """Main entry point."""
    logger = setup_logging()

    logger.info("🚀 Simple Bokeh Build & Push Tool")
    logger.info("=" * 50)

    # Check prerequisites
    if not os.path.exists("1-build_dualcambokeh_sdk.py"):
        logger.error("❌ Build script not found. Please run from Bokeh SDK root directory.")
        return 1

    # Check device
    device_id = check_device()
    if not device_id:
        logger.error("❌ No device connected. Please connect device and enable USB debugging.")
        return 1

    # Build
    if not build_bokeh():
        logger.error("❌ Build failed")
        return 1

    # Push
    if not push_to_device(device_id):
        logger.error("❌ Push failed")
        return 1

    logger.info("🎉 Build and Push completed successfully!")
    logger.info("📋 You can now run verification tests.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
