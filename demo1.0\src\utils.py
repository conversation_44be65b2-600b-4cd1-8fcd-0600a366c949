#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utility functions for Bokeh Verification Tool.
"""

import os
import sys
import time
import logging
import subprocess
from typing import List, Optional, Tuple, Dict, Any
import shutil

class Timer:
    """Simple timer class for measuring execution time."""
    
    def __init__(self):
        """Initialize the timer."""
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """Start the timer."""
        self.start_time = time.time()
        return self.start_time
    
    def stop(self):
        """Stop the timer and return elapsed time."""
        self.end_time = time.time()
        return self.elapsed()
    
    def elapsed(self) -> float:
        """
        Get elapsed time in seconds.
        
        Returns:
            Elapsed time in seconds.
        """
        if self.start_time is None:
            return 0.0
        
        end = self.end_time if self.end_time is not None else time.time()
        return end - self.start_time

def setup_logging(level: int = logging.INFO, log_file: Optional[str] = None):
    """
    Set up logging configuration.
    
    Args:
        level: Logging level.
        log_file: Path to log file. If None, logs to console only.
    """
    # Create logger
    logger = logging.getLogger('bokeh_verify')
    logger.setLevel(level)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Create file handler if log_file is specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

def run_command(cmd: str, shell: bool = True, cwd: Optional[str] = None) -> Tuple[int, str, str]:
    """
    Run a shell command and return the result.
    
    Args:
        cmd: Command to run.
        shell: Whether to use shell.
        cwd: Working directory.
        
    Returns:
        Tuple of (return_code, stdout, stderr).
    """
    logger = logging.getLogger('bokeh_verify.utils')
    logger.debug(f"Running command: {cmd}")
    
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        shell=shell,
        cwd=cwd,
        universal_newlines=True
    )
    
    stdout, stderr = process.communicate()
    return_code = process.returncode
    
    if return_code != 0:
        logger.warning(f"Command failed with return code {return_code}")
        logger.debug(f"stderr: {stderr}")
    
    return return_code, stdout, stderr

def run_adb_command(cmd: str, device_id: Optional[str] = None) -> Tuple[int, str, str]:
    """
    Run an ADB command.
    
    Args:
        cmd: ADB command to run (without 'adb' prefix).
        device_id: Device ID to target. If None, uses the default device.
        
    Returns:
        Tuple of (return_code, stdout, stderr).
    """
    adb_cmd = f"adb"
    if device_id:
        adb_cmd += f" -s {device_id}"
    adb_cmd += f" {cmd}"
    
    return run_command(adb_cmd)

def get_connected_devices() -> List[str]:
    """
    Get a list of connected Android devices.
    
    Returns:
        List of device IDs.
    """
    _, stdout, _ = run_command("adb devices")
    
    devices = []
    for line in stdout.strip().split('\n')[1:]:
        if line and not line.startswith('*') and '\tdevice' in line:
            devices.append(line.split('\t')[0])
    
    return devices

def find_files(directory: str, pattern: str = None) -> List[str]:
    """
    Find files in a directory, optionally matching a pattern.
    
    Args:
        directory: Directory to search.
        pattern: Optional filename pattern to match.
        
    Returns:
        List of file paths.
    """
    result = []
    for root, _, files in os.walk(directory):
        for file in files:
            if pattern is None or pattern in file:
                result.append(os.path.join(root, file))
    return result

def ensure_directory(path: str) -> str:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        path: Directory path.
        
    Returns:
        The directory path.
    """
    os.makedirs(path, exist_ok=True)
    return path

def delete_directory_contents(directory: str):
    """
    Delete all contents of a directory without removing the directory itself.
    
    Args:
        directory: Directory to clean.
    """
    if not os.path.exists(directory):
        return
    
    for item in os.listdir(directory):
        item_path = os.path.join(directory, item)
        if os.path.isfile(item_path) or os.path.islink(item_path):
            os.unlink(item_path)
        elif os.path.isdir(item_path):
            shutil.rmtree(item_path)

def copy_file(src: str, dst: str) -> bool:
    """
    Copy a file from source to destination.
    
    Args:
        src: Source file path.
        dst: Destination file path.
        
    Returns:
        True if successful, False otherwise.
    """
    try:
        shutil.copy2(src, dst)
        return True
    except (IOError, OSError) as e:
        logging.getLogger('bokeh_verify.utils').error(f"Error copying file: {e}")
        return False
