import time
import os
import sys
import configparser
import subprocess
import threading

print(sys.argv)

def run_cmd(cmd):
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, shell=True)
    #subprocess.run(['cmd', '/c', f'start cmd /k "ssh"'])
    return result.stdout

def openMemDebug():
    os.system("adb shell setprop vendor.camera.mem.debug.enable 1")
    os.system("adb shell setprop persist.camera.bokeh.preview.enable.memory.trace 1")
    os.system("adb shell setprop vendor.camera.mem.debug.enable 1")

def openDump():
    dumpennable = '1'
    dumprender = '3'
    os.system("adb shell mkdir ./sdcard/Android/data/com.oplus.camera/vbdump/")
    os.system("adb shell setprop persist.camera.bokeh.preview.render.dump.level 3" )
    os.system("adb shell setprop persist.camera.bokeh.preview.depth.dump.level 3")
    os.system("adb shell setprop persist.camera.bokeh.preview.dump.rectify 3")
    os.system("adb shell setprop persist.camera.bokeh.preview.dump.depth.warp 3")
    os.system("adb shell setprop persist.camera.bokeh.preview.dump.create.newdir 0")
    

def closeDump():
    dumpennable = '0'
    dumprender = '0'
    os.system("adb shell setprop persist.camera.bokeh.preview.render.dump.level " + dumprender )
    os.system("adb shell setprop persist.camera.bokeh.preview.depth.dump.level " + dumpennable)
    os.system("adb shell setprop persist.camera.bokeh.preview.dump.rectify " + dumpennable)
    os.system("adb shell setprop persist.camera.bokeh.preview.dump.depth.warp " + dumpennable)

def openLogTrace():
    os.system("adb logcat -G 64M")
    os.system("adb shell setprop persist.camera.bokeh.preview.log.status 63")
    os.system("adb shell setprop persist.camera.bokeh.preview.enable.perf.trace 1")
    os.system("adb shell setprop rw.hcs.atrace.enable 1")

def pushRtbLib():
    os.system("adb root")
    os.system("adb remount")
    os.system("adb disable-verity")
    # push HCS / RTB so
    os.system("adb shell mkdir ./data/local/tmp/PreviewBokeh/")
    os.system("adb push "+ PC_libpath +"arm64-v8a/libOPAlgoCamPreviewDualPortrait.so ./odm/lib64")
    os.system("adb push "+ PC_libpath +"arm64-v8a/libhcsfwk.so ./odm/lib64")
    os.system("adb push "+ PC_libpath +"arm64-v8a/libhcsutils.so ./odm/lib64")
    os.system("adb push "+ PC_libpath +"arm64-v8a/testpreview  ./data/local/tmp/PreviewBokeh/")
    os.system("adb shell chmod 777 ./data/local/tmp/PreviewBokeh/testpreview")
    os.system("adb shell am force-stop com.oplus.camera")
    #os.system("adb push "+ PC_libpath +"arm64-v8a/libhcsext-null.so ./odm/lib64")
    #os.system("adb push "+ PC_libpath +"arm64-v8a/libhcsext-gpu.so ./odm/lib64")
    #os.system("adb push "+ PC_libpath +"arm64-v8a/libhcsext-npu.so ./odm/lib64")
    #os.system("adb push "+ PC_libpath +"arm64-v8a/libhcsext-eva.so ./odm/lib64")
    #os.system("adb push "+ PC_libpath +"arm64-v8a/libhcsext-cpu.so ./odm/lib64")
    #os.system("adb push "+ PC_libpath +"arm64-v8a/libhcs_be.so ./odm/lib64")
    os.system("adb shell kill `pidof vendor.qti.camera.provider-service_64`")
    os.system("adb shell setenforce 0")

def closeLogSet():
    os.system("adb shell setprop persist.log.tag F")
    #os.system("adb shell setprop persist.log.tag V")
    os.system("adb shell setprop persist.log.tag.DualCamDepthPreviewEngine V")
    os.system("adb shell setprop persist.logd.flowctrl.quota.rows 65535")
    os.system("adb shell rm -rf ./sdcard/Android/data/com.oplus.camera/vbdump/*")

def clocker(time_s):
    for i in range(time_s):
        time.sleep(1)
        print("运行时间:", i, "s", end='\r', flush=True)
    print("\n\n\n\n\n", flush=True)

def buildDebug():
    print("开始编译debug版本, 等30s...")
    print(SshBuildDebugCmd)
    #tempThread = threading.Thread(target=clocker, args=(25,))
    #tempThread.start()
    process = subprocess.Popen(f"cmd.exe /c {SshBuildDebugCmd}", creationflags=subprocess.CREATE_NEW_CONSOLE)
    process.communicate() 
    #run_cmd(SshBuildDebugCmd)
    #tempThread.join()

def buildSts():
    print("开始编译Sts稳定性测试版本, 等120s...")
    print(SshBuildSTSCmd)
    process=subprocess.Popen(f"cmd.exe /c {SshBuildSTSCmd}", creationflags=subprocess.CREATE_NEW_CONSOLE)
    process.communicate() 

def cleanRemoteLib():
    print("开始清理Sts lib 防止误用...")
    print(SshCleanLibCmd)
    process=subprocess.Popen(f"cmd.exe /c {SshCleanLibCmd}", creationflags=subprocess.CREATE_NEW_CONSOLE)
    process.communicate() 

PC_libpath = ""
SshBuildDebugCmd = ""
SshBuildSTSCmd = ""
SshCleanLibCmd = ""
def readConfig():
    global PC_libpath
    global SshBuildDebugCmd
    global SshBuildSTSCmd
    # 创建ConfigParser对象
    config = configparser.ConfigParser()
    # 读取配置文件
    config.read('config.txt')
    # 读取config.txt的配置信息
    print("push_bokeh.py 读取config.txt内容，config.txt需要自己修改适配，部分的配置信息如下：")
    PC_libpath = config.get('config', 'PC_libpath')
    print("PC_libpath： \n  ", PC_libpath)
    SshBuildDebugCmd = config.get('config', 'SshBuildDebugCmd').strip('"') #并去除外层引号
    print("SshBuildDebugCmd: \n  ", SshBuildDebugCmd, "")
    SshBuildSTSCmd = config.get('config', 'SshBuildSTSCmd').strip('"')
    print("SshBuildSTSCmd: \n  ", SshBuildSTSCmd, "")
    SshCleanLibCmd = config.get('config', 'SshCleanLibCmd').strip('"')
    print("SshCleanLibCmd: \n  ", SshCleanLibCmd, "")

#adb shell "export LD_LIBRARY_PATH=/odm/lib64 && export ADSP_LIBRARY_PATH=/odm/lib/rfsa/adsp && LD_HWASAN=1 /data/local/tmp/PreviewBokeh/testpreview /data/local/tmp/PreviewBokeh/input_list.txt CaseStableTest 10"
#adb shell "export LD_LIBRARY_PATH=/odm/lib64 && export ADSP_LIBRARY_PATH=/odm/lib/rfsa/adsp && LD_HWASAN=1 /data/local/tmp/PreviewBokeh/testpreview /data/local/tmp/PreviewBokeh/input_list.txt CaseStableTest 10"
#os.system("adb shell \"export LD_LIBRARY_PATH=/odm/lib64 && export ADSP_LIBRARY_PATH=/odm/lib/rfsa/adsp && LD_HWASAN=1 /data/local/tmp/PreviewBokeh/testpreview /data/local/tmp/PreviewBokeh/input_list.txt CaseStableTest 10\"")
if __name__ == "__main__":
    readConfig()
    #buildDebug()
    pushRtbLib()
    #os.system("adb shell \"export LD_LIBRARY_PATH=/odm/lib64 && export ADSP_LIBRARY_PATH=/odm/lib/rfsa/adsp && LD_HWASAN=1 /data/local/tmp/PreviewBokeh/testpreview /data/local/tmp/PreviewBokeh/input_list.txt CaseStableTest 10\"")
    os.system("adb shell \"export LD_LIBRARY_PATH=/odm/lib64 && export ADSP_LIBRARY_PATH=/odm/lib/rfsa/adsp && LD_HWASAN=1 /data/local/tmp/PreviewBokeh/testpreview /data/local/tmp/PreviewBokeh/input_list.txt\"")

    #closeDump()
