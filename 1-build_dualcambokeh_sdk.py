# -*- coding: utf-8 -*-
#########################################################
## Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd.
##
## File: - buil_dualcambokeh_sdk.py
## Description: build sdk and pack sdk
## Version: 1.0
## Date : 2024/09/07
## Author: dxd
## ----------------------Revision History: --------------------
##  <author>    <data>    <version >     <desc>
##  dxd       2024/09/07    1.0       creat init file
#########################################################
import os
import shutil
import sys
import common # 导入公共配置模块
import argparse
# --- 使用 common 中的辅助函数 ---
mkdir = common.mkdir_if_not_exists
checkfile = common.check_file_exists
findAllfile = common.find_all_files # 注意：common.py 中的实现返回列表
GetBuildSDKTime = common.get_build_sdk_time
GetPlatform = common.get_platform
get_ndk_version = common.get_ndk_version
prop_ndkBuild_ndkVersion = common.prop_ndkBuild_ndkVersion

#STPE1 set Build Patamter ...........................................................
print("set paramter begin..........................................................")
if not common.check_ndk_version():
    sys.exit(1)
SDK_buildtime = GetBuildSDKTime()
print("build sdk timepoint ", SDK_buildtime)

# --- 从 common 获取默认值 ---
DualCamBokeh_SdkVersion = common.DEFAULT_SDK_VERSION
Bokeh_OpenCLBinary_Version = DualCamBokeh_SdkVersion

# --- 系列映射 ---
seriesMap = common.SERIES_MAP

# --- 参数解析 (保持不变，但使用 common 中的默认值) ---
parser = argparse.ArgumentParser(description='Build DualCamBokeh SDK')
parser.add_argument('-s', '--series', type=str, required=False, choices=list(seriesMap.keys()), default=common.DEFAULT_SERIES,
                   help='product series in format, e.g. X7U, X8, X8U, X9, X9U')
parser.add_argument('-l', '--lib_type', type=int, choices=[0, 1, 2], default=common.DEFAULT_LIB_TYPE,
                   help='library type: 0=normal, 1=converity, 2=asan (default: %(default)s)')
parser.add_argument('-c', '--clean_type', type=int, choices=[0, 1], default=common.DEFAULT_CLEAN_TYPE,
                   help='Need close log and dump: 0=false, 1=true (default: %(default)s)')
parser.add_argument('-e', '--need_copy_etc', type=int, choices=[0, 1], default=common.DEFAULT_NEED_COPY_ETC,
                   help='Need copy ETC: 0=false, 1=true (default: %(default)s)')
args = parser.parse_args()

try:
    series = args.series
    soc_config = common.get_soc_config(series)
    if not soc_config["TARGET_SOC"]:
        raise ValueError("Unsupported series: ", series)
    print("Series argument: ", series)
except Exception as e:
    print("Error parsing arguments: ", e)
    print("Set default argument")
    soc_config = common.get_soc_config(common.DEFAULT_SERIES)
    print("Series argument: ", series)

LIB_TYPE = args.lib_type
CLEAN_TYPE = args.clean_type
NEED_COPY_ETC = args.need_copy_etc

# --- 获取 SOC 相关配置 ---
TARGET_SOC = soc_config["TARGET_SOC"]
SERIES = soc_config["SERIES"]
PROJECT = soc_config["PROJECT"]
ODNN_VERSION = soc_config["ODNN_VERSION"]
QNN_VERSION = soc_config["QNN_VERSION"]
ETC_PATH = soc_config["ETC_PATH"] # 注意：ETC_PATH 可能为 None
AI_SDK = soc_config["AI_SDK"]
VENDOR = soc_config["VENDOR"]
MTK_SOC = soc_config["MTK_SOC"]
CL_BINARY = soc_config["CL_BINARY"]
APS_PATH = soc_config["APS_PATH"]

print("SOC argument: ", TARGET_SOC)
print("Series argument: ", SERIES)
print("PROJECT argument: ", PROJECT)
print("CL_BINARY argument: ", CL_BINARY)
print("APS_PATH argument: ", APS_PATH)

if not TARGET_SOC:
    print("Error: Could not determine configuration for target. Exiting: ", TARGET_SOC)
    sys.exit(1)

# --- 获取构建类型和清理标志 ---
ENABLE_RELEASE_CLEAN = common.get_clean_flag(CLEAN_TYPE)
print("Enable Release Clean:", ENABLE_RELEASE_CLEAN)

# --- 处理 SDK 版本后缀 ---
if LIB_TYPE == 2:
    BUILD_TYPE = "hwasan"
    DualCamBokeh_SdkVersion += ".HWASAN"
elif LIB_TYPE == 1:
     BUILD_TYPE = "coverity"
     DualCamBokeh_SdkVersion += ".COVERITY"
elif LIB_TYPE == 0:
     BUILD_TYPE = "normal"
else:
    print("no compile type:", LIB_TYPE)
print("Final SDK Version:", DualCamBokeh_SdkVersion)
print("Build Type:", BUILD_TYPE)
#...................................................

BUILD_DATE_INPUT = SDK_buildtime
REAR_PORTRAIT_BOKEH_LIB_VERSION_INPUT = "REAR_PORTRAIT_BOKEH_LIB_VERSION:"\
                                      + "TARGET_SOC_" + TARGET_SOC \
                                      + "_VERSION_" + DualCamBokeh_SdkVersion   \
                                      + "_BUILD_TIME_" + BUILD_DATE_INPUT
print("REAR_PORTRAIT_BOKEH_LIB_VERSION ", REAR_PORTRAIT_BOKEH_LIB_VERSION_INPUT)
BOKEH_OPENCL_BIN_VERSION_INPUT = Bokeh_OpenCLBinary_Version.replace(".", "")
print("BOKEH_OPENCL_BIN_VERSION_INPUT ", BOKEH_OPENCL_BIN_VERSION_INPUT)
BUILD_CLEAN_OBJ = True     # To release the SDK to the public, please set this to True, the build will be slower.
                            # To temporarily debug, you can set this to False, the build will be faster.

print("TARGET_SOC ", TARGET_SOC)
print("ODNN_VERSION ", ODNN_VERSION)
print("QNN_VERSION ", QNN_VERSION)

# --- 使用 common 中的 NDK 配置 ---
ndk_path = common.NDK_PATH
TARGET_ARCH_ABI = common.TARGET_ARCH_ABI
NDK_PROJECT_PATH = common.NDK_PROJECT_PATH
APP_BUILD_SCRIPT = common.APP_BUILD_SCRIPT
NDK_APPLICATION_MK = common.NDK_APPLICATION_MK

# --- 构建命令 ---
buildcmd = ndk_path + " " \
        " NDK_PROJECT_PATH=" + NDK_PROJECT_PATH + " " \
        " APP_BUILD_SCRIPT=" + APP_BUILD_SCRIPT + " " \
        " NDK_APPLICATION_MK=" + NDK_APPLICATION_MK +  " " \
        " VENDOR=" + VENDOR +  " " \
        " ODNN_VERSION=" + ODNN_VERSION + " " \
        " QNN_VERSION=" + QNN_VERSION + " " \
        " TARGET_ARCH_ABI=" + TARGET_ARCH_ABI + " "\
        " REAR_PORTRAIT_BOKEH_LIB_VERSION_INPUT=" + REAR_PORTRAIT_BOKEH_LIB_VERSION_INPUT + \
        " BOKEH_OPENCL_BIN_VERSION_INPUT=" + BOKEH_OPENCL_BIN_VERSION_INPUT + \
        " BUILD_DATE_INPUT=" + BUILD_DATE_INPUT + \
        " BUILD_TYPE=" + BUILD_TYPE + \
        " ENABLE_RELEASE_CLEAN=" + ENABLE_RELEASE_CLEAN + \
        " MTK_SOC=" + MTK_SOC + \
        " CL_BINARY=" + CL_BINARY.split(".")[0] + \
        " -j16"

print("buildcmd " + buildcmd)
print("set paramter end..........................................................")
#................................................................................

#STPE2 Clean SO..........................................
if BUILD_CLEAN_OBJ:
    print("remove libs && obj begin")
    # 使用 checkfile 检查目录是否存在
    if checkfile("libs"):
        shutil.rmtree("libs")
    if checkfile("obj"):
        shutil.rmtree("obj")
    print("remove libs && obj end")
#................................................................................

#STEP3 build sdk.....................................................
import time # 确保 time 被导入
time_start=time.time()
os.system(buildcmd)
time_end=time.time()
print("build sdk consume time ", time_end-time_start, 's')
#................................................................................

#STEP4 checkout build result.....................................................
buildstatus = False
lib_path = os.path.join("libs", TARGET_ARCH_ABI, common.LIB_NAME)
if checkfile(lib_path):
    buildstatus = True
    print("build all project success")
else:
    print("build all project failed - not found: ", lib_path)
    sys.exit(1)
#................................................................................

#STEP5 copy sdk file.............................................................
# buildstatus = False
if buildstatus:
    first_commit_id = None
    print("check Git usable...")
    errorlevel = os.system("git --version >nul 2>&1")
    print("git --version errorlevel ", errorlevel)
    if errorlevel != 0:
        print("Git is not usable, cannot get commit ID.")
    else:
        print("check is there an repo...")
        errorlevel = os.system("git rev-parse --is-inside-work-tree >nul 2>&1")
        print("git rev-parse errorlevel ", errorlevel)
        if errorlevel != 0:
            print("not git repo, cannot get commit ID.")
        else:
            print("get first commit ID...")
            try:
                first_commit_id = (os.popen("git rev-parse HEAD").readlines())[0].strip()
                print("first_commit_id ", first_commit_id)
            except IndexError:
                print("Failed to get commit ID from git rev-parse HEAD")
            except Exception as e:
                print("Error getting commit ID: ", e)

    # --- 使用 common 中的目录和文件名 ---
    sdk_release_base_dir = common.SDK_RELEASE_BASE_DIR
    mkdir(sdk_release_base_dir) # 确保基础目录存在

    SDK_TARGT_SOC_FOLDER = os.path.join(sdk_release_base_dir, TARGET_SOC)
    mkdir(SDK_TARGT_SOC_FOLDER) # 确保 SOC 特定目录存在

    SDK_releasepath = SDK_TARGT_SOC_FOLDER + "/" + "OPAlgoCamCaptureDualPortrait_" + TARGET_SOC + "_" + SERIES + "_V" + DualCamBokeh_SdkVersion + "_T" + SDK_buildtime + "/"
    print("sdkreleasepath is: ", SDK_releasepath)
    mkdir(SDK_releasepath)
    mkdir(os.path.join(SDK_releasepath, "modelLib"))
    mkdir(os.path.join(SDK_releasepath, "lib"))
    mkdir(os.path.join(SDK_releasepath, "include"))
    mkdir(os.path.join(SDK_releasepath, "symbol"))

    # --- 写入 Commit ID  ---
    if first_commit_id:
        commitid_txt = os.path.join(SDK_releasepath, common.COMMIT_ID_FILENAME)
        try:
            with open(commitid_txt, 'w') as writerid:
                writerid.write(first_commit_id + '\n') # 添加换行符
            print("Written commit ID to: ", commitid_txt)
        except IOError as e:
            print("Error writing commit ID file: ", e)
    else:
        print("Skipping commit ID file creation.")

    # --- 写入 Commit Info ---
    commitInfo_txt = os.path.join(SDK_releasepath, common.COMMIT_INFO_FILENAME)
    os.system('git log --oneline -n 10 > {file}'.format(file = commitInfo_txt))

    # --- 创建 .bat 文件 ---
    print("create 1_unlock.bat")
    unlock_bat_path = os.path.join(SDK_releasepath, common.UNLOCK_BAT_FILENAME)
    try:
        with open(unlock_bat_path, 'w') as writes:
            writes.write("adb root \n")
            writes.write("adb remount \n")
            writes.write("adb reboot \n")
            writes.write("pause")
    except IOError as e:
        print("Error writing: ", common.UNLOCK_BAT_FILENAME, e)

    print("create 2_pushlib.bat")
    try:
        for project in seriesMap[SERIES]["projects"]:
            project_name = "2_pushlib_" + project + ".bat"      # project : huanghe zhujiang changjiang
            pushlib= SDK_releasepath + "/" + project_name
            print("create project_name", project_name)
            with open(pushlib, 'w') as writes:
                writes.write("adb remount \n")
                writes.write("adb shell am force-stop com.coloros.gallery3d \n")
                writes.write("adb push ./lib/libOPAlgoCamCaptureDualPortrait.so   /odm/lib64/ \n")
                writes.write("adb push ./modelLib/.       /odm/etc/camera/dualcam_capture_bokeh/. \n")
                command = "adb push rectify_params/" + project + "/.  /odm/etc/camera/dualcam_capture_bokeh/. \n"
                print("command ", command)
                writes.write(command)
                writes.write("adb shell pkill -u cameraserver \n")
                writes.write("pause")
            writes.close()
    except IOError as e:
        print("Error writing: ", common.PUSHLIB_BAT_FILENAME, e)

    print("create 3_GetLog.bat")
    getlog_bat_path = os.path.join(SDK_releasepath, common.GETLOG_BAT_FILENAME)
    try:
        with open(getlog_bat_path, 'w') as writes:
            writes.write("set filename=%date:~0,4%%date:~5,2%%date:~8,2%%time:~0,2%%time:~3,2%%time:~6,2% \n")
            writes.write("set filename=%filename: =% \n")
            writes.write("adb logcat -c\n")
            writes.write("adb logcat -G 100M \n")
            writes.write("adb logcat > %filename%.log \n") # 添加 .log 后缀
            writes.write("pause")
    except IOError as e:
        print("Error writing: ", common.GETLOG_BAT_FILENAME, e)

    print("create 4_CopyToAps.bat")
    copy_to_aps_path = os.path.join(SDK_releasepath, common.COPY_TO_APS_FILENAME)
    try:
        if "X9" == args.series:
            with open(copy_to_aps_path, 'w') as writes:
                writes.write("setlocal enabledelayedexpansion \n")
                writes.write("set str=Z:\\code\\24081\n")
                if LIB_TYPE == 2:
                    writes.write("copy .\\lib\\. %str%{}\\common\\libs\\arm64-hwasan\\ \n ".format(APS_PATH))
                elif LIB_TYPE == 0:
                    writes.write("copy .\\lib\\. %str%{}\\common\\libs\\arm64-v8a\\ \n ".format(APS_PATH))
                else:
                    print("no compile type:", LIB_TYPE)
                writes.write("copy .\\symbol\\. %str%{}\\common\\libs\\arm64-symbol\\ \n ".format(APS_PATH))
                writes.write('del /Q /F /S "%str%{}\\common\\etc\\dualcam_capture_bokeh\\*.*" \n'.format(APS_PATH))
                writes.write("copy .\\modelLib\\. %str%{}\\common\\etc\\dualcam_capture_bokeh\\ \n".format(APS_PATH))
                writes.write("copy .\\rectify_params\\changjiang\\. %str%{}\\24081\etc\\ \n".format(APS_PATH))
                writes.write("copy .\\rectify_params\\zhujiang\\. %str%{}\\24087\etc\\ \n".format(APS_PATH))
                writes.write("pause")
        else:
            print("Warning: Configuration for target not found: ", args.series)
    except IOError as e:
        print("Error writing: ", common.COPY_TO_APS_FILENAME, e)

    # --- 复制 so 文件 ---
    print("copy so file begin")
    try:
        # 源路径
        source_lib_path = os.path.join("libs", TARGET_ARCH_ABI, common.LIB_NAME)
        source_symbol_path = os.path.join("obj", "local", TARGET_ARCH_ABI, common.LIB_NAME)
        # 目标路径
        target_lib_path = os.path.join(SDK_releasepath, "lib", common.LIB_NAME)
        target_symbol_path = os.path.join(SDK_releasepath, "symbol", common.LIB_NAME)

        shutil.copy(source_lib_path, target_lib_path)
        print("Copied: ", source_lib_path, target_lib_path)
        shutil.copy(source_symbol_path, target_symbol_path)
        print("Copied: ", source_symbol_path, target_symbol_path)
    except FileNotFoundError as e:
        print("Error copying SO files:. Check build output.", e)
    except Exception as e:
        print("Error copying SO files: ", e)

    # --- 复制头文件 ---
    print("copy head file begin")
    try:
        # 源路径
        header_bokeh_source = os.path.join(common.INTERFACE_ASADAPTER_INCLUDE_PATH, common.HEADER_BOKEH)
        header_common_source = os.path.join(common.INTERFACE_ASADAPTER_INCLUDE_PATH, common.HEADER_BOKEH_COMMON)
        # 目标路径
        include_dir = os.path.join(SDK_releasepath, "include")

        shutil.copy(header_bokeh_source, include_dir)
        print("Copied: ", header_bokeh_source, include_dir)
        shutil.copy(header_common_source, include_dir)
        print("Copied: ", header_common_source, include_dir)
    except FileNotFoundError as e:
        print("Error copying header files. Check paths.", e)
    except Exception as e:
        print("Error copying header files: ", e)
    print("copy head file end")

    # --- 复制 rectify_param.json ---
    json_source_dir = os.path.join("..", "..", "3rdparty", "rectify", SERIES)
    rectify_params_dir = os.path.join(SDK_releasepath, "rectify_params")
    if checkfile(json_source_dir):
        print("copy rectify json file beign", json_source_dir)
        shutil.copytree(json_source_dir, rectify_params_dir)
    else:
        print("Warning: Rectify param file not found: ", json_source_dir)

    # --- 复制 CL 二进制文件 ---
    model_lib_dir = os.path.join(SDK_releasepath, "modelLib")
    cl_binary_source = os.path.join(common.CL_BINARY_DIR, CL_BINARY)
    if cl_binary_source and checkfile(cl_binary_source):
        try:
            shutil.copy(cl_binary_source, model_lib_dir)
            print("Copied: ", cl_binary_source, model_lib_dir)
        except Exception as e:
            print("Error copying CL binary file: ", cl_binary_source, e)
    else:
        print("Warning: CL binary file not found: ", cl_binary_source)

    # --- 复制模型文件 ---
    print("copy model file begin")
    model_file_path = common.THIRDPARTY_AIMODEL_PATH + "/" + TARGET_SOC + "_ODNN_" + ODNN_VERSION + "_" + AI_SDK + "_" + QNN_VERSION + "/"
    print("model_file_path ", model_file_path)

    all_model_files = []
    all_model_files = findAllfile(model_file_path, all_model_files)

    # --- 执行模型文件复制 ---
    if model_lib_dir:
        if not all_model_files:
             print("Warning: No model files found in: ", model_file_path)
        for single_model_path in all_model_files:
            try:
                print("Copying: ", single_model_path)
                shutil.copy(single_model_path, model_lib_dir)
            except Exception as e:
                print("Error copying model file: ", single_model_path, e)
        print("Copy model files end. Target: ", model_lib_dir)
    else:
        print("Skipping model file copy as target directory is not set.")

    # --- gradle copy models ---
    if NEED_COPY_ETC == True:
        mkdir(ETC_PATH)
        if ETC_PATH and checkfile(ETC_PATH): # 检查 ETC_PATH 是否有效且存在
            shutil.rmtree(ETC_PATH)
            # 复制 models 到 ETC 目录 (如果适用)
            shutil.copytree(model_lib_dir, ETC_PATH)
            print("grandle Copy model files end. Target ", ETC_PATH)

            # 复制 rectify_param.json 到 ETC 目录 (如果适用)
            if checkfile(rectify_params_dir):
                etc_rectify_params_dir = os.path.join(ETC_PATH, "rectify_params")
                print("gradle copy rectify json file beign", rectify_params_dir)
                shutil.copytree(rectify_params_dir, etc_rectify_params_dir)
            else:
                print("Warning: Rectify param file not found: ", json_source_dir, etc_rectify_params_dir)
        else:
            print("Warning: NEED_COPY_ETC is True, but ETC_PATH is invalid or does not exist. Skipping ETC copy: ", ETC_PATH)

        # --- 写入 Commit Info ---
        with open(common.GRADLE_RELEASE_NOTE, 'w') as f:
            f.write(common.DEFAULT_SDK_VERSION + '\n')
        with open(common.GRADLE_RELEASE_NOTE, 'a') as f:
            f.write(SDK_releasepath + '\n')
        os.system('git log --oneline -n 10 >> {file}'.format(file = common.GRADLE_RELEASE_NOTE))

    print("SDK path ", SDK_releasepath)
    print("Build and packaging process finished.")
#................................................................................

#STEP5 Generate opencl binary file.............................................................
print("STEP5 Generate OpenCL Binary file.")
if GetPlatform() == 'Windows':
    # adb tools
    command = "adb"
    p = os.popen(command)
    clbin_name = p.read()
    if "Android Debug Bridge version" in clbin_name:
        print("adb is installed")
    else:
        print("adb is not installed")
        exit(0)
    # get first devices id
    devices_id_list = common.get_device_id()
    if len(devices_id_list) == 1:
        print("Find devices:", devices_id_list[0])
        device_id = devices_id_list[0]
    elif len(devices_id_list) > 1:
        print("Find devices, please select one: ", devices_id_list)
        print("Multi devices, default use first one: ", devices_id_list[0])
        device_id = devices_id_list[0]
    else:
        print("NO devices found")
        exit(1)
    #
    device_product = common.get_device_product()
    print("device_product ", device_product)
    # match device model
    for product in PROJECT:
        print("product ", product)
        if product in common.DEVICE_PRODUCT_MAP.keys():
            device_model = common.DEVICE_PRODUCT_MAP[product]
            print("device_model ", device_model[0])
            if device_model[0] in device_product.keys():
                print("Find devices: ", device_product)
                os.system("python 1-run_OpenCLBinSim.py")
                exit(1)
            else:
                local_device_model = common.get_key(device_product, device_id)
                local_device_product = common.get_key(common.DEVICE_PRODUCT_MAP, local_device_model)
                compiled_device_product = common.get_key(common.DEVICE_PRODUCT_MAP, device_model)
                print("compiled_device_product ", device_model, compiled_device_product)
                print("local_device_model ", local_device_model, local_device_product)
                print("Device not matched. expected: %s, actual: %s" % (compiled_device_product, local_device_product))
                # special case for same SOC project
                if (compiled_device_product[0] == "zhufeng" and local_device_product[0] == "Hummer") or \
                    (compiled_device_product[0] == "zhufeng" and local_device_product[0] == "Dodge"):
                    print("Special case for Hummer. builded: %s, actual: %s" % (compiled_device_product, local_device_product))
                    os.system("python 1-run_OpenCLBinSim.py")
                    exit(1)
#................................................................................
