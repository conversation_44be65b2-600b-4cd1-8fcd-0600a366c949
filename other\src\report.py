#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Report generation module for Bokeh Verification Tool.

This module handles generating reports from comparison results.
"""

import os
import json
import logging
import datetime
from typing import Dict, List, Any, Optional

from config import Config
from utils import ensure_directory
from compare import ResultComparator

class ReportGenerator:
    """Generator for Bokeh verification reports."""
    
    def __init__(self, config: Config):
        """
        Initialize the report generator.
        
        Args:
            config: Configuration object.
        """
        self.logger = logging.getLogger('bokeh_verify.report')
        self.config = config
        self.comparator = ResultComparator(config)
    
    def generate(self, comparison_results: Dict[str, Any], output_dir: str, format: str = 'html') -> str:
        """
        Generate a report from comparison results.
        
        Args:
            comparison_results: Comparison results.
            output_dir: Directory to save the report to.
            format: Report format (html, text, json).
            
        Returns:
            Path to the generated report.
        """
        self.logger.info(f"Generating {format} report in {output_dir}")
        
        # Create output directory
        ensure_directory(output_dir)
        
        # Generate report based on format
        if format == 'html':
            return self._generate_html_report(comparison_results, output_dir)
        elif format == 'text':
            return self._generate_text_report(comparison_results, output_dir)
        elif format == 'json':
            return self._generate_json_report(comparison_results, output_dir)
        else:
            self.logger.error(f"Unknown report format: {format}")
            return ""
    
    def _generate_html_report(self, comparison_results: Dict[str, Any], output_dir: str) -> str:
        """
        Generate an HTML report.
        
        Args:
            comparison_results: Comparison results.
            output_dir: Directory to save the report to.
            
        Returns:
            Path to the generated report.
        """
        # Save comparison images
        images_dir = os.path.join(output_dir, 'diff_images')
        saved_images = self.comparator.save_comparison_images(
            comparison_results['comparison_results'],
            images_dir
        )
        
        # Create report file path
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = os.path.join(output_dir, f"report_{timestamp}.html")
        
        # Generate HTML content
        html_content = self._generate_html_content(comparison_results, saved_images)
        
        # Write HTML file
        with open(report_path, 'w') as f:
            f.write(html_content)
        
        self.logger.info(f"HTML report generated at {report_path}")
        return report_path
    
    def _generate_text_report(self, comparison_results: Dict[str, Any], output_dir: str) -> str:
        """
        Generate a text report.
        
        Args:
            comparison_results: Comparison results.
            output_dir: Directory to save the report to.
            
        Returns:
            Path to the generated report.
        """
        # Create report file path
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = os.path.join(output_dir, f"report_{timestamp}.txt")
        
        # Generate text content
        text_content = self._generate_text_content(comparison_results)
        
        # Write text file
        with open(report_path, 'w') as f:
            f.write(text_content)
        
        self.logger.info(f"Text report generated at {report_path}")
        return report_path
    
    def _generate_json_report(self, comparison_results: Dict[str, Any], output_dir: str) -> str:
        """
        Generate a JSON report.
        
        Args:
            comparison_results: Comparison results.
            output_dir: Directory to save the report to.
            
        Returns:
            Path to the generated report.
        """
        # Create report file path
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = os.path.join(output_dir, f"report_{timestamp}.json")
        
        # Prepare JSON data (remove non-serializable objects)
        json_data = comparison_results.copy()
        for result in json_data['comparison_results']:
            result.pop('diff_image', None)
        
        # Write JSON file
        with open(report_path, 'w') as f:
            json.dump(json_data, f, indent=2)
        
        self.logger.info(f"JSON report generated at {report_path}")
        return report_path
    
    def _generate_html_content(self, comparison_results: Dict[str, Any], saved_images: Dict[str, str]) -> str:
        """
        Generate HTML content for the report.
        
        Args:
            comparison_results: Comparison results.
            saved_images: Dictionary mapping filenames to saved image paths.
            
        Returns:
            HTML content.
        """
        # Extract data
        passed = comparison_results['pass']
        max_diff = comparison_results['max_diff']
        failed_modules = comparison_results['failed_modules']
        missing_files = comparison_results['missing_files']
        results = comparison_results['comparison_results']
        result_path = comparison_results['result_path']
        reference_path = comparison_results['reference_path']
        
        # Generate HTML
        html = f"""<!DOCTYPE html>
<html>
<head>
    <title>Bokeh Verification Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1 {{ color: #333; }}
        .summary {{ margin-bottom: 20px; padding: 10px; border-radius: 5px; }}
        .pass {{ background-color: #dff0d8; color: #3c763d; }}
        .fail {{ background-color: #f2dede; color: #a94442; }}
        table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        tr.pass {{ background-color: #dff0d8; }}
        tr.fail {{ background-color: #f2dede; }}
        .diff-image {{ max-width: 100%; height: auto; margin-top: 10px; }}
    </style>
</head>
<body>
    <h1>Bokeh Verification Report</h1>
    
    <div class="summary {('pass' if passed else 'fail')}">
        <h2>Summary: {('PASSED' if passed else 'FAILED')}</h2>
        <p>Timestamp: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>Max Difference: {max_diff:.2f}</p>
        <p>Result Path: {result_path}</p>
        <p>Reference Path: {reference_path}</p>
    </div>
    
    {('<h3>Failed Modules:</h3><ul>' + ''.join([f'<li>{module}</li>' for module in failed_modules]) + '</ul>') if failed_modules else ''}
    
    {('<h3>Missing Files:</h3><ul>' + ''.join([f'<li>{filename}</li>' for filename in missing_files]) + '</ul>') if missing_files else ''}
    
    <h3>Detailed Results:</h3>
    <table>
        <tr>
            <th>Filename</th>
            <th>Module</th>
            <th>Difference</th>
            <th>Threshold</th>
            <th>Status</th>
        </tr>
"""
        
        # Add rows for each result
        for result in sorted(results, key=lambda x: x['filename']):
            filename = result['filename']
            module = result['module']
            diff = result['diff']
            threshold = result['threshold']
            passed = result['passed']
            
            html += f"""
        <tr class="{'pass' if passed else 'fail'}">
            <td>{filename}</td>
            <td>{module}</td>
            <td>{diff:.2f}</td>
            <td>{threshold:.2f}</td>
            <td>{'PASS' if passed else 'FAIL'}</td>
        </tr>"""
            
            # Add diff image if available
            if filename in saved_images:
                relative_path = os.path.relpath(saved_images[filename], output_dir)
                html += f"""
        <tr>
            <td colspan="5">
                <img src="{relative_path}" class="diff-image" alt="Difference image for {filename}">
            </td>
        </tr>"""
        
        html += """
    </table>
</body>
</html>
"""
        
        return html
    
    def _generate_text_content(self, comparison_results: Dict[str, Any]) -> str:
        """
        Generate text content for the report.
        
        Args:
            comparison_results: Comparison results.
            
        Returns:
            Text content.
        """
        # Extract data
        passed = comparison_results['pass']
        max_diff = comparison_results['max_diff']
        failed_modules = comparison_results['failed_modules']
        missing_files = comparison_results['missing_files']
        results = comparison_results['comparison_results']
        result_path = comparison_results['result_path']
        reference_path = comparison_results['reference_path']
        
        # Generate text
        text = f"""Bokeh Verification Report
========================

Summary: {'PASSED' if passed else 'FAILED'}
Timestamp: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Max Difference: {max_diff:.2f}
Result Path: {result_path}
Reference Path: {reference_path}

"""
        
        if failed_modules:
            text += "Failed Modules:\n"
            for module in failed_modules:
                text += f"- {module}\n"
            text += "\n"
        
        if missing_files:
            text += "Missing Files:\n"
            for filename in missing_files:
                text += f"- {filename}\n"
            text += "\n"
        
        text += "Detailed Results:\n"
        text += "----------------\n"
        text += f"{'Filename':<40} {'Module':<15} {'Difference':<15} {'Threshold':<15} {'Status':<10}\n"
        text += "-" * 100 + "\n"
        
        # Add rows for each result
        for result in sorted(results, key=lambda x: x['filename']):
            filename = result['filename']
            module = result['module']
            diff = result['diff']
            threshold = result['threshold']
            passed = result['passed']
            
            text += f"{filename:<40} {module:<15} {diff:<15.2f} {threshold:<15.2f} {'PASS' if passed else 'FAIL'}\n"
        
        return text
