[device]
# 设备系列 / Device series (默认X8U)
series = X8U
# 设备ID（留空自动检测） / Device ID (leave empty for auto-detection)
id =

# SSH配置（用于远程构建）/ SSH configuration (for remote build)
ssh_host = *************
ssh_user = 80398453
ssh_port = 22
ssh_password =
# SSH密钥文件（免密登录）/ SSH key file (passwordless login)
ssh_key_file = ~/.ssh/id_rsa

[build]
# 远程SDK路径 / Remote SDK path
sdk_path = ~/zx_code/bokeh/oppo_dualDepth/DualCamDepthEngine/jni/ArcBokehSim
# 构建脚本 / Build script
build_script = 1-build_dualcambokeh_sdk.py
# 库类型 / Library type (0=normal, 1=coverity, 2=asan)
lib_type = 0
# 清理类型 / Clean type (0=false, 1=true)
clean_type = 0
# 是否自动构建 / Auto build
auto_build = True
# 是否自动推送 / Auto push
auto_push = True

[push]
# 库路径 / Library path (本地下载后的路径)
lib_path = libs/arm64-v8a
# 库名称 / Library name
lib_name = libOPAlgoCamCaptureDualPortrait.so
# 设备上的目标库路径 / Target library path on device
target_lib_path = /odm/lib64
# 测试程序名称 / Test program name
test_program = test_arcbokehsim
# 设备上的测试程序路径 / Test program path on device
test_program_path = /data/local/tmp/BokehSim

[dump]
# 设备上的dump基础路径 / Base path for dumps on device
dump_base_path = /sdcard/Android/data/com.oplus.camera/files/spdebug/bokehdump
# 本地dump路径 / Local path for pulled dumps
dump_local_path = outputs/vbdump
# depth模块的dump级别 / Dump level for depth module
depth_dump_level = 3
# render模块的dump级别 / Dump level for render module
render_dump_level = 3
# 是否为每次dump创建新目录 / Whether to create a new directory for each dump
create_new_dir = 0

[compare]
# 参考数据路径 / Path to reference data
reference_dir = verify_data/golden
# 全局比较阈值 / Global threshold for comparison
global_threshold = 5

[output]
# 结果输出目录 / Directory for output results
dir = verify_results
# 是否保存所有图像 / Whether to save all images
save_all_images = True
# 是否只保存差异图像 / Whether to save only diff images
save_diff_only = False
