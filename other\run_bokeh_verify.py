#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple runner script for Bokeh verification.
Bokeh验证的简单运行脚本。

This script provides the simplest way to run Bokeh verification.
该脚本提供运行Bokeh验证的最简单方法。
"""

import os
import sys
import subprocess

def check_prerequisites():
    """
    Check if all prerequisites are met.
    检查是否满足所有前提条件。
    """
    print("🔍 Checking prerequisites... / 检查前提条件...")
    
    # Check if config file exists / 检查配置文件是否存在
    if not os.path.exists('config.simple.ini'):
        print("❌ Configuration file not found / 未找到配置文件")
        print("Please run: python init_project.py --all")
        print("请运行: python init_project.py --all")
        return False
    
    # Check if build script exists / 检查构建脚本是否存在
    if not os.path.exists('1-build_dualcambokeh_sdk.py'):
        print("❌ Build script not found / 未找到构建脚本")
        print("Please ensure you are in the Bokeh SDK root directory")
        print("请确保您在Bokeh SDK根目录中")
        return False
    
    # Check if adb is available / 检查adb是否可用
    try:
        result = subprocess.run(['adb', 'version'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ ADB not found / 未找到ADB")
            return False
    except FileNotFoundError:
        print("❌ ADB not found in PATH / 在PATH中未找到ADB")
        return False
    
    # Check if device is connected / 检查设备是否连接
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
        lines = result.stdout.strip().split('\n')[1:]  # Skip header
        devices = [line for line in lines if line and '\tdevice' in line]
        if not devices:
            print("❌ No device connected / 未连接设备")
            print("Please connect an Android device and enable USB debugging")
            print("请连接Android设备并启用USB调试")
            return False
        else:
            device_id = devices[0].split('\t')[0]
            print(f"✅ Device found / 找到设备: {device_id}")
    except Exception as e:
        print(f"❌ Error checking devices / 检查设备时出错: {e}")
        return False
    
    print("✅ All prerequisites met / 所有前提条件都满足")
    return True

def run_auto_mode():
    """
    Run the auto mode.
    运行自动模式。
    """
    print("\n" + "=" * 60)
    print("🚀 Starting Bokeh Auto Verification / 开始Bokeh自动验证")
    print("=" * 60)
    
    # Run the main script / 运行主脚本
    cmd = [sys.executable, 'bokeh_verify.py', '--mode', 'auto']
    
    print(f"📋 Running command / 运行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except Exception as e:
        print(f"❌ Error running verification / 运行验证时出错: {e}")
        return 1

def main():
    """
    Main entry point.
    主入口点。
    """
    print("Bokeh Verification Tool - Simple Runner")
    print("Bokeh验证工具 - 简单运行器")
    print("=" * 60)
    
    # Check prerequisites / 检查前提条件
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please fix the issues above.")
        print("❌ 前提条件不满足。请修复上述问题。")
        return 1
    
    # Run auto mode / 运行自动模式
    return_code = run_auto_mode()
    
    # Summary / 总结
    print("\n" + "=" * 60)
    if return_code == 0:
        print("🎉 Verification completed successfully! / 验证成功完成！")
        print("📋 Check the output directory for results / 检查输出目录中的结果")
    else:
        print("❌ Verification failed! / 验证失败！")
        print("📋 Check the logs for error details / 检查日志中的错误详情")
    
    return return_code

if __name__ == "__main__":
    sys.exit(main())
