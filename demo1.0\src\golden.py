#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Golden data management module for Bokeh verification tool.
Bokeh验证工具的Golden数据管理模块。

This module handles:
- Saving current results as golden reference data
- Loading golden reference data for comparison
- Managing different golden data versions

该模块处理：
- 将当前结果保存为golden参考数据
- 加载golden参考数据用于对比
- 管理不同版本的golden数据
"""

import os
import shutil
import logging
import json
from datetime import datetime
from typing import Optional, Dict, List
from pathlib import Path

from utils import ensure_directory


class GoldenDataManager:
    """
    Manager for golden reference data.
    Golden参考数据管理器。
    """

    def __init__(self, config):
        """
        Initialize the golden data manager.
        初始化golden数据管理器。

        Args / 参数:
            config: Configuration object / 配置对象
        """
        self.config = config
        self.logger = logging.getLogger('bokeh_verify.golden')

        # Golden data base directory / Golden数据基础目录
        self.golden_base_dir = self.config.get('golden', 'base_dir', 'golden_data')
        ensure_directory(self.golden_base_dir)

        # Current golden data directory / 当前golden数据目录
        self.current_golden_dir = os.path.join(self.golden_base_dir, 'current')

        # Metadata file / 元数据文件
        self.metadata_file = os.path.join(self.golden_base_dir, 'metadata.json')

    def save_as_golden(self, result_dir: str, version_name: Optional[str] = None) -> bool:
        """
        Save current results as golden reference data.
        将当前结果保存为golden参考数据。

        Args / 参数:
            result_dir: Directory containing current results / 包含当前结果的目录
            version_name: Optional version name for this golden data / 此golden数据的可选版本名称

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        if not os.path.exists(result_dir):
            self.logger.error(f"Result directory not found / 结果目录未找到: {result_dir}")
            return False

        try:
            # Generate version name if not provided / 如果未提供则生成版本名称
            if not version_name:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                version_name = f"golden_{timestamp}"

            # Create versioned golden directory / 创建版本化的golden目录
            versioned_dir = os.path.join(self.golden_base_dir, version_name)

            # Remove existing directory if it exists / 如果存在则删除现有目录
            if os.path.exists(versioned_dir):
                shutil.rmtree(versioned_dir)

            # Copy results to versioned directory / 将结果复制到版本化目录
            shutil.copytree(result_dir, versioned_dir)
            self.logger.info(f"Results saved as golden data / 结果已保存为golden数据: {versioned_dir}")

            # Update current golden link / 更新当前golden链接
            if os.path.exists(self.current_golden_dir):
                if os.path.islink(self.current_golden_dir):
                    os.unlink(self.current_golden_dir)
                else:
                    shutil.rmtree(self.current_golden_dir)

            # Create symlink to current version / 创建到当前版本的符号链接
            try:
                os.symlink(versioned_dir, self.current_golden_dir)
            except OSError:
                # Fallback to copy if symlink fails (Windows without admin rights)
                # 如果符号链接失败则回退到复制（Windows无管理员权限）
                shutil.copytree(versioned_dir, self.current_golden_dir)

            # Update metadata / 更新元数据
            self._update_metadata(version_name, result_dir)

            self.logger.info(f"Golden data updated successfully / Golden数据更新成功: {version_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to save golden data / 保存golden数据失败: {str(e)}")
            return False

    def get_current_golden_dir(self) -> Optional[str]:
        """
        Get the current golden data directory.
        获取当前golden数据目录。

        Returns / 返回:
            Path to current golden data directory or None if not exists / 当前golden数据目录路径，如果不存在则返回None
        """
        if os.path.exists(self.current_golden_dir):
            return self.current_golden_dir
        return None

    def list_golden_versions(self) -> List[Dict]:
        """
        List all available golden data versions.
        列出所有可用的golden数据版本。

        Returns / 返回:
            List of golden data version information / golden数据版本信息列表
        """
        versions = []

        if not os.path.exists(self.golden_base_dir):
            return versions

        for item in os.listdir(self.golden_base_dir):
            item_path = os.path.join(self.golden_base_dir, item)
            if os.path.isdir(item_path) and item != 'current':
                # Get creation time / 获取创建时间
                stat = os.stat(item_path)
                created_time = datetime.fromtimestamp(stat.st_ctime)

                versions.append({
                    'name': item,
                    'path': item_path,
                    'created': created_time.strftime('%Y-%m-%d %H:%M:%S')
                })

        # Sort by creation time / 按创建时间排序
        versions.sort(key=lambda x: x['created'], reverse=True)
        return versions

    def load_golden_version(self, version_name: str) -> bool:
        """
        Load a specific golden data version as current.
        加载特定的golden数据版本作为当前版本。

        Args / 参数:
            version_name: Name of the version to load / 要加载的版本名称

        Returns / 返回:
            True if successful / 如果成功则返回True
        """
        version_path = os.path.join(self.golden_base_dir, version_name)

        if not os.path.exists(version_path):
            self.logger.error(f"Golden version not found / Golden版本未找到: {version_name}")
            return False

        try:
            # Remove current golden link / 删除当前golden链接
            if os.path.exists(self.current_golden_dir):
                if os.path.islink(self.current_golden_dir):
                    os.unlink(self.current_golden_dir)
                else:
                    shutil.rmtree(self.current_golden_dir)

            # Create new link / 创建新链接
            try:
                os.symlink(version_path, self.current_golden_dir)
            except OSError:
                # Fallback to copy / 回退到复制
                shutil.copytree(version_path, self.current_golden_dir)

            self.logger.info(f"Loaded golden version / 已加载golden版本: {version_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to load golden version / 加载golden版本失败: {str(e)}")
            return False

    def _update_metadata(self, version_name: str, source_dir: str):
        """
        Update metadata file with new version information.
        使用新版本信息更新元数据文件。

        Args / 参数:
            version_name: Name of the version / 版本名称
            source_dir: Source directory of the data / 数据源目录
        """
        metadata = {}

        # Load existing metadata / 加载现有元数据
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            except Exception:
                pass

        # Add new version info / 添加新版本信息
        metadata[version_name] = {
            'created': datetime.now().isoformat(),
            'source_dir': source_dir,
            'current': True
        }

        # Mark other versions as not current / 将其他版本标记为非当前
        for key in metadata:
            if key != version_name:
                metadata[key]['current'] = False

        # Save metadata / 保存元数据
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.warning(f"Failed to update metadata / 更新元数据失败: {str(e)}")

    def get_metadata(self) -> Dict:
        """
        Get golden data metadata.
        获取golden数据元数据。

        Returns / 返回:
            Metadata dictionary / 元数据字典
        """
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                pass
        return {}
