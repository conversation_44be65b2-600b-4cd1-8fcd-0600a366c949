[device]
# Device series (e.g., X8U, X9)
series = X8U
# Device ID for ADB (leave empty for auto-detection)
id = 
# SSH configuration for remote build
ssh_host = 
ssh_user = 
ssh_port = 22

[build]
# Path to the Bokeh SDK
sdk_path = 
# Build script name
build_script = build.py
# Build arguments
build_args = debug
# Output path for built libraries
output_path = libs

[push]
# Path to libraries
lib_path = libs/arm64-v8a
# Library name
lib_name = libOPAlgoCamCaptureDualPortrait.so
# Target library path on device
target_lib_path = /odm/lib64
# Test program name
test_program = test_arcbokehsim
# Test program path on device
test_program_path = /data/local/tmp/BokehSim
# Path to model files
model_path = 
# Target model path on device
target_model_path = /odm/etc/camera/dualcam_capture_bokeh
# Path to rectify parameters
rectify_params_path = 

[dump]
# Base path for dumps on device
dump_base_path = /sdcard/Android/data/com.oplus.camera/files/spdebug/bokehdump
# Local path for pulled dumps
dump_local_path = outputs/vbdump
# Dump level for depth module (0-3)
depth_dump_level = 3
# Dump level for render module (0-3)
render_dump_level = 3
# Dump level for rectify module (0-3)
rectify_dump_level = 3
# Dump level for depth warp module (0-3)
depth_warp_dump_level = 3
# Whether to create a new directory for each dump
create_new_dir = 0

[compare]
# Path to reference data
reference_dir = verify_data/golden
# Global threshold for comparison
global_threshold = 5
# Module-specific thresholds
module_threshold_depth = 5
module_threshold_render = 5
module_threshold_rectify = 5
module_threshold_final = 3

[output]
# Directory for output results
dir = verify_results
# Whether to save all images
save_all_images = True
# Whether to save only diff images
save_diff_only = False
